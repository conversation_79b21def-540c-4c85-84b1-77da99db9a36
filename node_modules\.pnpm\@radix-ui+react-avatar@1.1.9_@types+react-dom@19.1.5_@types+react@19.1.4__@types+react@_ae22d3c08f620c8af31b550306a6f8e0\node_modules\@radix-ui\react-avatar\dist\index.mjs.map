{"version": 3, "sources": ["../src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle';\n  }\n  if (!src) {\n    return 'error';\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading';\n}\n\nfunction useImageLoadingStatus(\n  src: string | undefined,\n  { referrerPolicy, crossOrigin }: AvatarImageProps\n) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef<HTMLImageElement | null>(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>(() =>\n    resolveLoadingStatus(image, src)\n  );\n\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n\n  useLayoutEffect(() => {\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      setLoadingStatus(status);\n    };\n\n    if (!image) return;\n\n    const handleLoad = updateStatus('loaded');\n    const handleError = updateStatus('error');\n    image.addEventListener('load', handleLoad);\n    image.addEventListener('error', handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === 'string') {\n      image.crossOrigin = crossOrigin;\n    }\n\n    return () => {\n      image.removeEventListener('load', handleLoad);\n      image.removeEventListener('error', handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n\n  return loadingStatus;\n}\n\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAoCtB;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAM9F,IAAM,SAAe;AAAA,EACnB,CAAC,OAAiC,iBAAiB;AACjD,UAAM,EAAE,eAAe,GAAG,YAAY,IAAI;AAC1C,UAAM,CAAC,oBAAoB,qBAAqB,IAAU,eAA6B,MAAM;AAC7F,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA,4BAA4B;AAAA,QAE5B,8BAAC,UAAU,MAAV,EAAgB,GAAG,aAAa,KAAK,cAAc;AAAA;AAAA,IACtD;AAAA,EAEJ;AACF;AAEA,OAAO,cAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,KAAK,wBAAwB,MAAM;AAAA,IAAC,GAAG,GAAG,WAAW,IAAI;AAChF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,qBAAqB,sBAAsB,KAAK,UAAU;AAChE,UAAM,4BAA4B,eAAe,CAAC,WAA+B;AAC/E,4BAAsB,MAAM;AAC5B,cAAQ,2BAA2B,MAAM;AAAA,IAC3C,CAAC;AAED,oBAAgB,MAAM;AACpB,UAAI,uBAAuB,QAAQ;AACjC,kCAA0B,kBAAkB;AAAA,MAC9C;AAAA,IACF,GAAG,CAAC,oBAAoB,yBAAyB,CAAC;AAElD,WAAO,uBAAuB,WAC5B,oBAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,cAAc,KAAU,IAC1D;AAAA,EACN;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,eAAe,SAAS,GAAG,cAAc,IAAI;AACrD,UAAM,UAAU,iBAAiB,eAAe,aAAa;AAC7D,UAAM,CAAC,WAAW,YAAY,IAAU,eAAS,YAAY,MAAS;AAEtE,IAAM,gBAAU,MAAM;AACpB,UAAI,YAAY,QAAW;AACzB,cAAM,UAAU,OAAO,WAAW,MAAM,aAAa,IAAI,GAAG,OAAO;AACnE,eAAO,MAAM,OAAO,aAAa,OAAO;AAAA,MAC1C;AAAA,IACF,GAAG,CAAC,OAAO,CAAC;AAEZ,WAAO,aAAa,QAAQ,uBAAuB,WACjD,oBAAC,UAAU,MAAV,EAAgB,GAAG,eAAe,KAAK,cAAc,IACpD;AAAA,EACN;AACF;AAEA,eAAe,cAAc;AAI7B,SAAS,qBAAqB,OAAgC,KAAkC;AAC9F,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,KAAK;AACrB,UAAM,MAAM;AAAA,EACd;AACA,SAAO,MAAM,YAAY,MAAM,eAAe,IAAI,WAAW;AAC/D;AAEA,SAAS,sBACP,KACA,EAAE,gBAAgB,YAAY,GAC9B;AACA,QAAM,aAAa,cAAc;AACjC,QAAM,WAAiB,aAAgC,IAAI;AAC3D,QAAM,SAAS,MAAM;AACnB,QAAI,CAAC,WAAY,QAAO;AACxB,QAAI,CAAC,SAAS,SAAS;AACrB,eAAS,UAAU,IAAI,OAAO,MAAM;AAAA,IACtC;AACA,WAAO,SAAS;AAAA,EAClB,GAAG;AAEH,QAAM,CAAC,eAAe,gBAAgB,IAAU;AAAA,IAA6B,MAC3E,qBAAqB,OAAO,GAAG;AAAA,EACjC;AAEA,kBAAgB,MAAM;AACpB,qBAAiB,qBAAqB,OAAO,GAAG,CAAC;AAAA,EACnD,GAAG,CAAC,OAAO,GAAG,CAAC;AAEf,kBAAgB,MAAM;AACpB,UAAM,eAAe,CAAC,WAA+B,MAAM;AACzD,uBAAiB,MAAM;AAAA,IACzB;AAEA,QAAI,CAAC,MAAO;AAEZ,UAAM,aAAa,aAAa,QAAQ;AACxC,UAAM,cAAc,aAAa,OAAO;AACxC,UAAM,iBAAiB,QAAQ,UAAU;AACzC,UAAM,iBAAiB,SAAS,WAAW;AAC3C,QAAI,gBAAgB;AAClB,YAAM,iBAAiB;AAAA,IACzB;AACA,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,cAAc;AAAA,IACtB;AAEA,WAAO,MAAM;AACX,YAAM,oBAAoB,QAAQ,UAAU;AAC5C,YAAM,oBAAoB,SAAS,WAAW;AAAA,IAChD;AAAA,EACF,GAAG,CAAC,OAAO,aAAa,cAAc,CAAC;AAEvC,SAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW;", "names": []}