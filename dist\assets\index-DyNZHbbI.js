function j0(f,h){for(var m=0;m<h.length;m++){const s=h[m];if(typeof s!="string"&&!Array.isArray(s)){for(const g in s)if(g!=="default"&&!(g in f)){const E=Object.getOwnPropertyDescriptor(s,g);E&&Object.defineProperty(f,g,E.get?E:{enumerable:!0,get:()=>s[g]})}}}return Object.freeze(Object.defineProperty(f,Symbol.toStringTag,{value:"Module"}))}(function(){const h=document.createElement("link").relList;if(h&&h.supports&&h.supports("modulepreload"))return;for(const g of document.querySelectorAll('link[rel="modulepreload"]'))s(g);new MutationObserver(g=>{for(const E of g)if(E.type==="childList")for(const M of E.addedNodes)M.tagName==="LINK"&&M.rel==="modulepreload"&&s(M)}).observe(document,{childList:!0,subtree:!0});function m(g){const E={};return g.integrity&&(E.integrity=g.integrity),g.referrerPolicy&&(E.referrerPolicy=g.referrerPolicy),g.crossOrigin==="use-credentials"?E.credentials="include":g.crossOrigin==="anonymous"?E.credentials="omit":E.credentials="same-origin",E}function s(g){if(g.ep)return;g.ep=!0;const E=m(g);fetch(g.href,E)}})();function H0(f){return f&&f.__esModule&&Object.prototype.hasOwnProperty.call(f,"default")?f.default:f}var hf={exports:{}},Un={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td;function C0(){if(Td)return Un;Td=1;var f=Symbol.for("react.transitional.element"),h=Symbol.for("react.fragment");function m(s,g,E){var M=null;if(E!==void 0&&(M=""+E),g.key!==void 0&&(M=""+g.key),"key"in g){E={};for(var U in g)U!=="key"&&(E[U]=g[U])}else E=g;return g=E.ref,{$$typeof:f,type:s,key:M,ref:g!==void 0?g:null,props:E}}return Un.Fragment=h,Un.jsx=m,Un.jsxs=m,Un}var zd;function q0(){return zd||(zd=1,hf.exports=C0()),hf.exports}var C=q0(),vf={exports:{}},I={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Md;function B0(){if(Md)return I;Md=1;var f=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),E=Symbol.for("react.consumer"),M=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),X=Symbol.iterator;function Q(r){return r===null||typeof r!="object"?null:(r=X&&r[X]||r["@@iterator"],typeof r=="function"?r:null)}var ft={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,nt={};function ot(r,D,q){this.props=r,this.context=D,this.refs=nt,this.updater=q||ft}ot.prototype.isReactComponent={},ot.prototype.setState=function(r,D){if(typeof r!="object"&&typeof r!="function"&&r!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,r,D,"setState")},ot.prototype.forceUpdate=function(r){this.updater.enqueueForceUpdate(this,r,"forceUpdate")};function L(){}L.prototype=ot.prototype;function rt(r,D,q){this.props=r,this.context=D,this.refs=nt,this.updater=q||ft}var et=rt.prototype=new L;et.constructor=rt,P(et,ot.prototype),et.isPureReactComponent=!0;var _t=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},Et=Object.prototype.hasOwnProperty;function Rt(r,D,q,j,B,ut){return q=ut.ref,{$$typeof:f,type:r,key:D,ref:q!==void 0?q:null,props:ut}}function k(r,D){return Rt(r.type,D,void 0,void 0,void 0,r.props)}function Mt(r){return typeof r=="object"&&r!==null&&r.$$typeof===f}function zl(r){var D={"=":"=0",":":"=2"};return"$"+r.replace(/[=:]/g,function(q){return D[q]})}var Pt=/\/+/g;function Ot(r,D){return typeof r=="object"&&r!==null&&r.key!=null?zl(""+r.key):D.toString(36)}function ul(){}function It(r){switch(r.status){case"fulfilled":return r.value;case"rejected":throw r.reason;default:switch(typeof r.status=="string"?r.then(ul,ul):(r.status="pending",r.then(function(D){r.status==="pending"&&(r.status="fulfilled",r.value=D)},function(D){r.status==="pending"&&(r.status="rejected",r.reason=D)})),r.status){case"fulfilled":return r.value;case"rejected":throw r.reason}}throw r}function Tt(r,D,q,j,B){var ut=typeof r;(ut==="undefined"||ut==="boolean")&&(r=null);var W=!1;if(r===null)W=!0;else switch(ut){case"bigint":case"string":case"number":W=!0;break;case"object":switch(r.$$typeof){case f:case h:W=!0;break;case N:return W=r._init,Tt(W(r._payload),D,q,j,B)}}if(W)return B=B(r),W=j===""?"."+Ot(r,0):j,_t(B)?(q="",W!=null&&(q=W.replace(Pt,"$&/")+"/"),Tt(B,D,q,"",function(il){return il})):B!=null&&(Mt(B)&&(B=k(B,q+(B.key==null||r&&r.key===B.key?"":(""+B.key).replace(Pt,"$&/")+"/")+W)),D.push(B)),1;W=0;var dt=j===""?".":j+":";if(_t(r))for(var xt=0;xt<r.length;xt++)j=r[xt],ut=dt+Ot(j,xt),W+=Tt(j,D,q,ut,B);else if(xt=Q(r),typeof xt=="function")for(r=xt.call(r),xt=0;!(j=r.next()).done;)j=j.value,ut=dt+Ot(j,xt++),W+=Tt(j,D,q,ut,B);else if(ut==="object"){if(typeof r.then=="function")return Tt(It(r),D,q,j,B);throw D=String(r),Error("Objects are not valid as a React child (found: "+(D==="[object Object]"?"object with keys {"+Object.keys(r).join(", ")+"}":D)+"). If you meant to render a collection of children, use an array instead.")}return W}function T(r,D,q){if(r==null)return r;var j=[],B=0;return Tt(r,j,"","",function(ut){return D.call(q,ut,B++)}),j}function H(r){if(r._status===-1){var D=r._result;D=D(),D.then(function(q){(r._status===0||r._status===-1)&&(r._status=1,r._result=q)},function(q){(r._status===0||r._status===-1)&&(r._status=2,r._result=q)}),r._status===-1&&(r._status=0,r._result=D)}if(r._status===1)return r._result.default;throw r._result}var R=typeof reportError=="function"?reportError:function(r){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var D=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof r=="object"&&r!==null&&typeof r.message=="string"?String(r.message):String(r),error:r});if(!window.dispatchEvent(D))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",r);return}console.error(r)};function vt(){}return I.Children={map:T,forEach:function(r,D,q){T(r,function(){D.apply(this,arguments)},q)},count:function(r){var D=0;return T(r,function(){D++}),D},toArray:function(r){return T(r,function(D){return D})||[]},only:function(r){if(!Mt(r))throw Error("React.Children.only expected to receive a single React element child.");return r}},I.Component=ot,I.Fragment=m,I.Profiler=g,I.PureComponent=rt,I.StrictMode=s,I.Suspense=A,I.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,I.__COMPILER_RUNTIME={__proto__:null,c:function(r){return F.H.useMemoCache(r)}},I.cache=function(r){return function(){return r.apply(null,arguments)}},I.cloneElement=function(r,D,q){if(r==null)throw Error("The argument must be a React element, but you passed "+r+".");var j=P({},r.props),B=r.key,ut=void 0;if(D!=null)for(W in D.ref!==void 0&&(ut=void 0),D.key!==void 0&&(B=""+D.key),D)!Et.call(D,W)||W==="key"||W==="__self"||W==="__source"||W==="ref"&&D.ref===void 0||(j[W]=D[W]);var W=arguments.length-2;if(W===1)j.children=q;else if(1<W){for(var dt=Array(W),xt=0;xt<W;xt++)dt[xt]=arguments[xt+2];j.children=dt}return Rt(r.type,B,void 0,void 0,ut,j)},I.createContext=function(r){return r={$$typeof:M,_currentValue:r,_currentValue2:r,_threadCount:0,Provider:null,Consumer:null},r.Provider=r,r.Consumer={$$typeof:E,_context:r},r},I.createElement=function(r,D,q){var j,B={},ut=null;if(D!=null)for(j in D.key!==void 0&&(ut=""+D.key),D)Et.call(D,j)&&j!=="key"&&j!=="__self"&&j!=="__source"&&(B[j]=D[j]);var W=arguments.length-2;if(W===1)B.children=q;else if(1<W){for(var dt=Array(W),xt=0;xt<W;xt++)dt[xt]=arguments[xt+2];B.children=dt}if(r&&r.defaultProps)for(j in W=r.defaultProps,W)B[j]===void 0&&(B[j]=W[j]);return Rt(r,ut,void 0,void 0,null,B)},I.createRef=function(){return{current:null}},I.forwardRef=function(r){return{$$typeof:U,render:r}},I.isValidElement=Mt,I.lazy=function(r){return{$$typeof:N,_payload:{_status:-1,_result:r},_init:H}},I.memo=function(r,D){return{$$typeof:p,type:r,compare:D===void 0?null:D}},I.startTransition=function(r){var D=F.T,q={};F.T=q;try{var j=r(),B=F.S;B!==null&&B(q,j),typeof j=="object"&&j!==null&&typeof j.then=="function"&&j.then(vt,R)}catch(ut){R(ut)}finally{F.T=D}},I.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},I.use=function(r){return F.H.use(r)},I.useActionState=function(r,D,q){return F.H.useActionState(r,D,q)},I.useCallback=function(r,D){return F.H.useCallback(r,D)},I.useContext=function(r){return F.H.useContext(r)},I.useDebugValue=function(){},I.useDeferredValue=function(r,D){return F.H.useDeferredValue(r,D)},I.useEffect=function(r,D,q){var j=F.H;if(typeof q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return j.useEffect(r,D)},I.useId=function(){return F.H.useId()},I.useImperativeHandle=function(r,D,q){return F.H.useImperativeHandle(r,D,q)},I.useInsertionEffect=function(r,D){return F.H.useInsertionEffect(r,D)},I.useLayoutEffect=function(r,D){return F.H.useLayoutEffect(r,D)},I.useMemo=function(r,D){return F.H.useMemo(r,D)},I.useOptimistic=function(r,D){return F.H.useOptimistic(r,D)},I.useReducer=function(r,D,q){return F.H.useReducer(r,D,q)},I.useRef=function(r){return F.H.useRef(r)},I.useState=function(r){return F.H.useState(r)},I.useSyncExternalStore=function(r,D,q){return F.H.useSyncExternalStore(r,D,q)},I.useTransition=function(){return F.H.useTransition()},I.version="19.1.0",I}var _d;function _f(){return _d||(_d=1,vf.exports=B0()),vf.exports}var V=_f();const w0=H0(V),Y0=j0({__proto__:null,default:w0},[V]);var yf={exports:{}},jn={},gf={exports:{}},bf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Od;function G0(){return Od||(Od=1,function(f){function h(T,H){var R=T.length;T.push(H);t:for(;0<R;){var vt=R-1>>>1,r=T[vt];if(0<g(r,H))T[vt]=H,T[R]=r,R=vt;else break t}}function m(T){return T.length===0?null:T[0]}function s(T){if(T.length===0)return null;var H=T[0],R=T.pop();if(R!==H){T[0]=R;t:for(var vt=0,r=T.length,D=r>>>1;vt<D;){var q=2*(vt+1)-1,j=T[q],B=q+1,ut=T[B];if(0>g(j,R))B<r&&0>g(ut,j)?(T[vt]=ut,T[B]=R,vt=B):(T[vt]=j,T[q]=R,vt=q);else if(B<r&&0>g(ut,R))T[vt]=ut,T[B]=R,vt=B;else break t}}return H}function g(T,H){var R=T.sortIndex-H.sortIndex;return R!==0?R:T.id-H.id}if(f.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var E=performance;f.unstable_now=function(){return E.now()}}else{var M=Date,U=M.now();f.unstable_now=function(){return M.now()-U}}var A=[],p=[],N=1,X=null,Q=3,ft=!1,P=!1,nt=!1,ot=!1,L=typeof setTimeout=="function"?setTimeout:null,rt=typeof clearTimeout=="function"?clearTimeout:null,et=typeof setImmediate<"u"?setImmediate:null;function _t(T){for(var H=m(p);H!==null;){if(H.callback===null)s(p);else if(H.startTime<=T)s(p),H.sortIndex=H.expirationTime,h(A,H);else break;H=m(p)}}function F(T){if(nt=!1,_t(T),!P)if(m(A)!==null)P=!0,Et||(Et=!0,Ot());else{var H=m(p);H!==null&&Tt(F,H.startTime-T)}}var Et=!1,Rt=-1,k=5,Mt=-1;function zl(){return ot?!0:!(f.unstable_now()-Mt<k)}function Pt(){if(ot=!1,Et){var T=f.unstable_now();Mt=T;var H=!0;try{t:{P=!1,nt&&(nt=!1,rt(Rt),Rt=-1),ft=!0;var R=Q;try{l:{for(_t(T),X=m(A);X!==null&&!(X.expirationTime>T&&zl());){var vt=X.callback;if(typeof vt=="function"){X.callback=null,Q=X.priorityLevel;var r=vt(X.expirationTime<=T);if(T=f.unstable_now(),typeof r=="function"){X.callback=r,_t(T),H=!0;break l}X===m(A)&&s(A),_t(T)}else s(A);X=m(A)}if(X!==null)H=!0;else{var D=m(p);D!==null&&Tt(F,D.startTime-T),H=!1}}break t}finally{X=null,Q=R,ft=!1}H=void 0}}finally{H?Ot():Et=!1}}}var Ot;if(typeof et=="function")Ot=function(){et(Pt)};else if(typeof MessageChannel<"u"){var ul=new MessageChannel,It=ul.port2;ul.port1.onmessage=Pt,Ot=function(){It.postMessage(null)}}else Ot=function(){L(Pt,0)};function Tt(T,H){Rt=L(function(){T(f.unstable_now())},H)}f.unstable_IdlePriority=5,f.unstable_ImmediatePriority=1,f.unstable_LowPriority=4,f.unstable_NormalPriority=3,f.unstable_Profiling=null,f.unstable_UserBlockingPriority=2,f.unstable_cancelCallback=function(T){T.callback=null},f.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):k=0<T?Math.floor(1e3/T):5},f.unstable_getCurrentPriorityLevel=function(){return Q},f.unstable_next=function(T){switch(Q){case 1:case 2:case 3:var H=3;break;default:H=Q}var R=Q;Q=H;try{return T()}finally{Q=R}},f.unstable_requestPaint=function(){ot=!0},f.unstable_runWithPriority=function(T,H){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var R=Q;Q=T;try{return H()}finally{Q=R}},f.unstable_scheduleCallback=function(T,H,R){var vt=f.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?vt+R:vt):R=vt,T){case 1:var r=-1;break;case 2:r=250;break;case 5:r=1073741823;break;case 4:r=1e4;break;default:r=5e3}return r=R+r,T={id:N++,callback:H,priorityLevel:T,startTime:R,expirationTime:r,sortIndex:-1},R>vt?(T.sortIndex=R,h(p,T),m(A)===null&&T===m(p)&&(nt?(rt(Rt),Rt=-1):nt=!0,Tt(F,R-vt))):(T.sortIndex=r,h(A,T),P||ft||(P=!0,Et||(Et=!0,Ot()))),T},f.unstable_shouldYield=zl,f.unstable_wrapCallback=function(T){var H=Q;return function(){var R=Q;Q=H;try{return T.apply(this,arguments)}finally{Q=R}}}}(bf)),bf}var Dd;function X0(){return Dd||(Dd=1,gf.exports=G0()),gf.exports}var pf={exports:{}},Wt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nd;function Q0(){if(Nd)return Wt;Nd=1;var f=_f();function h(A){var p="https://react.dev/errors/"+A;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var N=2;N<arguments.length;N++)p+="&args[]="+encodeURIComponent(arguments[N])}return"Minified React error #"+A+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(){}var s={d:{f:m,r:function(){throw Error(h(522))},D:m,C:m,L:m,m,X:m,S:m,M:m},p:0,findDOMNode:null},g=Symbol.for("react.portal");function E(A,p,N){var X=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:g,key:X==null?null:""+X,children:A,containerInfo:p,implementation:N}}var M=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function U(A,p){if(A==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return Wt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Wt.createPortal=function(A,p){var N=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(h(299));return E(A,p,null,N)},Wt.flushSync=function(A){var p=M.T,N=s.p;try{if(M.T=null,s.p=2,A)return A()}finally{M.T=p,s.p=N,s.d.f()}},Wt.preconnect=function(A,p){typeof A=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,s.d.C(A,p))},Wt.prefetchDNS=function(A){typeof A=="string"&&s.d.D(A)},Wt.preinit=function(A,p){if(typeof A=="string"&&p&&typeof p.as=="string"){var N=p.as,X=U(N,p.crossOrigin),Q=typeof p.integrity=="string"?p.integrity:void 0,ft=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;N==="style"?s.d.S(A,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:X,integrity:Q,fetchPriority:ft}):N==="script"&&s.d.X(A,{crossOrigin:X,integrity:Q,fetchPriority:ft,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},Wt.preinitModule=function(A,p){if(typeof A=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var N=U(p.as,p.crossOrigin);s.d.M(A,{crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&s.d.M(A)},Wt.preload=function(A,p){if(typeof A=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var N=p.as,X=U(N,p.crossOrigin);s.d.L(A,N,{crossOrigin:X,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},Wt.preloadModule=function(A,p){if(typeof A=="string")if(p){var N=U(p.as,p.crossOrigin);s.d.m(A,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else s.d.m(A)},Wt.requestFormReset=function(A){s.d.r(A)},Wt.unstable_batchedUpdates=function(A,p){return A(p)},Wt.useFormState=function(A,p,N){return M.H.useFormState(A,p,N)},Wt.useFormStatus=function(){return M.H.useHostTransitionStatus()},Wt.version="19.1.0",Wt}var Rd;function kd(){if(Rd)return pf.exports;Rd=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(h){console.error(h)}}return f(),pf.exports=Q0(),pf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ud;function k0(){if(Ud)return jn;Ud=1;var f=X0(),h=_f(),m=kd();function s(t){var l="https://react.dev/errors/"+t;if(1<arguments.length){l+="?args[]="+encodeURIComponent(arguments[1]);for(var e=2;e<arguments.length;e++)l+="&args[]="+encodeURIComponent(arguments[e])}return"Minified React error #"+t+"; visit "+l+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function g(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function E(t){var l=t,e=t;if(t.alternate)for(;l.return;)l=l.return;else{t=l;do l=t,(l.flags&4098)!==0&&(e=l.return),t=l.return;while(t)}return l.tag===3?e:null}function M(t){if(t.tag===13){var l=t.memoizedState;if(l===null&&(t=t.alternate,t!==null&&(l=t.memoizedState)),l!==null)return l.dehydrated}return null}function U(t){if(E(t)!==t)throw Error(s(188))}function A(t){var l=t.alternate;if(!l){if(l=E(t),l===null)throw Error(s(188));return l!==t?null:t}for(var e=t,a=l;;){var n=e.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){e=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===e)return U(n),t;if(u===a)return U(n),l;u=u.sibling}throw Error(s(188))}if(e.return!==a.return)e=n,a=u;else{for(var i=!1,c=n.child;c;){if(c===e){i=!0,e=n,a=u;break}if(c===a){i=!0,a=n,e=u;break}c=c.sibling}if(!i){for(c=u.child;c;){if(c===e){i=!0,e=u,a=n;break}if(c===a){i=!0,a=u,e=n;break}c=c.sibling}if(!i)throw Error(s(189))}}if(e.alternate!==a)throw Error(s(190))}if(e.tag!==3)throw Error(s(188));return e.stateNode.current===e?t:l}function p(t){var l=t.tag;if(l===5||l===26||l===27||l===6)return t;for(t=t.child;t!==null;){if(l=p(t),l!==null)return l;t=t.sibling}return null}var N=Object.assign,X=Symbol.for("react.element"),Q=Symbol.for("react.transitional.element"),ft=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),nt=Symbol.for("react.strict_mode"),ot=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),rt=Symbol.for("react.consumer"),et=Symbol.for("react.context"),_t=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),Et=Symbol.for("react.suspense_list"),Rt=Symbol.for("react.memo"),k=Symbol.for("react.lazy"),Mt=Symbol.for("react.activity"),zl=Symbol.for("react.memo_cache_sentinel"),Pt=Symbol.iterator;function Ot(t){return t===null||typeof t!="object"?null:(t=Pt&&t[Pt]||t["@@iterator"],typeof t=="function"?t:null)}var ul=Symbol.for("react.client.reference");function It(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ul?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case P:return"Fragment";case ot:return"Profiler";case nt:return"StrictMode";case F:return"Suspense";case Et:return"SuspenseList";case Mt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case ft:return"Portal";case et:return(t.displayName||"Context")+".Provider";case rt:return(t._context.displayName||"Context")+".Consumer";case _t:var l=t.render;return t=t.displayName,t||(t=l.displayName||l.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Rt:return l=t.displayName||null,l!==null?l:It(t.type)||"Memo";case k:l=t._payload,t=t._init;try{return It(t(l))}catch{}}return null}var Tt=Array.isArray,T=h.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=m.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R={pending:!1,data:null,method:null,action:null},vt=[],r=-1;function D(t){return{current:t}}function q(t){0>r||(t.current=vt[r],vt[r]=null,r--)}function j(t,l){r++,vt[r]=t.current,t.current=l}var B=D(null),ut=D(null),W=D(null),dt=D(null);function xt(t,l){switch(j(W,l),j(ut,t),j(B,null),l.nodeType){case 9:case 11:t=(t=l.documentElement)&&(t=t.namespaceURI)?Pr(t):0;break;default:if(t=l.tagName,l=l.namespaceURI)l=Pr(l),t=Ir(l,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}q(B),j(B,t)}function il(){q(B),q(ut),q(W)}function Pl(t){t.memoizedState!==null&&j(dt,t);var l=B.current,e=Ir(l,t.type);l!==e&&(j(ut,t),j(B,e))}function Il(t){ut.current===t&&(q(B),q(ut)),dt.current===t&&(q(dt),_n._currentValue=R)}var te=Object.prototype.hasOwnProperty,ti=f.unstable_scheduleCallback,li=f.unstable_cancelCallback,rm=f.unstable_shouldYield,dm=f.unstable_requestPaint,Dl=f.unstable_now,mm=f.unstable_getCurrentPriorityLevel,Rf=f.unstable_ImmediatePriority,Uf=f.unstable_UserBlockingPriority,Cn=f.unstable_NormalPriority,hm=f.unstable_LowPriority,jf=f.unstable_IdlePriority,vm=f.log,ym=f.unstable_setDisableYieldValue,Ha=null,cl=null;function le(t){if(typeof vm=="function"&&ym(t),cl&&typeof cl.setStrictMode=="function")try{cl.setStrictMode(Ha,t)}catch{}}var fl=Math.clz32?Math.clz32:pm,gm=Math.log,bm=Math.LN2;function pm(t){return t>>>=0,t===0?32:31-(gm(t)/bm|0)|0}var qn=256,Bn=4194304;function Me(t){var l=t&42;if(l!==0)return l;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function wn(t,l,e){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var c=a&134217727;return c!==0?(a=c&~u,a!==0?n=Me(a):(i&=c,i!==0?n=Me(i):e||(e=c&~t,e!==0&&(n=Me(e))))):(c=a&~u,c!==0?n=Me(c):i!==0?n=Me(i):e||(e=a&~t,e!==0&&(n=Me(e)))),n===0?0:l!==0&&l!==n&&(l&u)===0&&(u=n&-n,e=l&-l,u>=e||u===32&&(e&4194048)!==0)?l:n}function Ca(t,l){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&l)===0}function Sm(t,l){switch(t){case 1:case 2:case 4:case 8:case 64:return l+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return l+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hf(){var t=qn;return qn<<=1,(qn&4194048)===0&&(qn=256),t}function Cf(){var t=Bn;return Bn<<=1,(Bn&62914560)===0&&(Bn=4194304),t}function ei(t){for(var l=[],e=0;31>e;e++)l.push(t);return l}function qa(t,l){t.pendingLanes|=l,l!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function xm(t,l,e,a,n,u){var i=t.pendingLanes;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=e,t.entangledLanes&=e,t.errorRecoveryDisabledLanes&=e,t.shellSuspendCounter=0;var c=t.entanglements,o=t.expirationTimes,b=t.hiddenUpdates;for(e=i&~e;0<e;){var z=31-fl(e),O=1<<z;c[z]=0,o[z]=-1;var S=b[z];if(S!==null)for(b[z]=null,z=0;z<S.length;z++){var x=S[z];x!==null&&(x.lane&=-536870913)}e&=~O}a!==0&&qf(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~l))}function qf(t,l,e){t.pendingLanes|=l,t.suspendedLanes&=~l;var a=31-fl(l);t.entangledLanes|=l,t.entanglements[a]=t.entanglements[a]|1073741824|e&4194090}function Bf(t,l){var e=t.entangledLanes|=l;for(t=t.entanglements;e;){var a=31-fl(e),n=1<<a;n&l|t[a]&l&&(t[a]|=l),e&=~n}}function ai(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ni(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function wf(){var t=H.p;return t!==0?t:(t=window.event,t===void 0?32:bd(t.type))}function Am(t,l){var e=H.p;try{return H.p=t,l()}finally{H.p=e}}var ee=Math.random().toString(36).slice(2),Jt="__reactFiber$"+ee,tl="__reactProps$"+ee,Le="__reactContainer$"+ee,ui="__reactEvents$"+ee,Em="__reactListeners$"+ee,Tm="__reactHandles$"+ee,Yf="__reactResources$"+ee,Ba="__reactMarker$"+ee;function ii(t){delete t[Jt],delete t[tl],delete t[ui],delete t[Em],delete t[Tm]}function Ke(t){var l=t[Jt];if(l)return l;for(var e=t.parentNode;e;){if(l=e[Le]||e[Jt]){if(e=l.alternate,l.child!==null||e!==null&&e.child!==null)for(t=ad(t);t!==null;){if(e=t[Jt])return e;t=ad(t)}return l}t=e,e=t.parentNode}return null}function Je(t){if(t=t[Jt]||t[Le]){var l=t.tag;if(l===5||l===6||l===13||l===26||l===27||l===3)return t}return null}function wa(t){var l=t.tag;if(l===5||l===26||l===27||l===6)return t.stateNode;throw Error(s(33))}function $e(t){var l=t[Yf];return l||(l=t[Yf]={hoistableStyles:new Map,hoistableScripts:new Map}),l}function Xt(t){t[Ba]=!0}var Gf=new Set,Xf={};function _e(t,l){We(t,l),We(t+"Capture",l)}function We(t,l){for(Xf[t]=l,t=0;t<l.length;t++)Gf.add(l[t])}var zm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Qf={},kf={};function Mm(t){return te.call(kf,t)?!0:te.call(Qf,t)?!1:zm.test(t)?kf[t]=!0:(Qf[t]=!0,!1)}function Yn(t,l,e){if(Mm(l))if(e===null)t.removeAttribute(l);else{switch(typeof e){case"undefined":case"function":case"symbol":t.removeAttribute(l);return;case"boolean":var a=l.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(l);return}}t.setAttribute(l,""+e)}}function Gn(t,l,e){if(e===null)t.removeAttribute(l);else{switch(typeof e){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttribute(l,""+e)}}function Cl(t,l,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttributeNS(l,e,""+a)}}var ci,Zf;function Fe(t){if(ci===void 0)try{throw Error()}catch(e){var l=e.stack.trim().match(/\n( *(at )?)/);ci=l&&l[1]||"",Zf=-1<e.stack.indexOf(`
    at`)?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ci+t+Zf}var fi=!1;function si(t,l){if(!t||fi)return"";fi=!0;var e=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(l){var O=function(){throw Error()};if(Object.defineProperty(O.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(O,[])}catch(x){var S=x}Reflect.construct(t,[],O)}else{try{O.call()}catch(x){S=x}t.call(O.prototype)}}else{try{throw Error()}catch(x){S=x}(O=t())&&typeof O.catch=="function"&&O.catch(function(){})}}catch(x){if(x&&S&&typeof x.stack=="string")return[x.stack,S.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],c=u[1];if(i&&c){var o=i.split(`
`),b=c.split(`
`);for(n=a=0;a<o.length&&!o[a].includes("DetermineComponentFrameRoot");)a++;for(;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;if(a===o.length||n===b.length)for(a=o.length-1,n=b.length-1;1<=a&&0<=n&&o[a]!==b[n];)n--;for(;1<=a&&0<=n;a--,n--)if(o[a]!==b[n]){if(a!==1||n!==1)do if(a--,n--,0>n||o[a]!==b[n]){var z=`
`+o[a].replace(" at new "," at ");return t.displayName&&z.includes("<anonymous>")&&(z=z.replace("<anonymous>",t.displayName)),z}while(1<=a&&0<=n);break}}}finally{fi=!1,Error.prepareStackTrace=e}return(e=t?t.displayName||t.name:"")?Fe(e):""}function _m(t){switch(t.tag){case 26:case 27:case 5:return Fe(t.type);case 16:return Fe("Lazy");case 13:return Fe("Suspense");case 19:return Fe("SuspenseList");case 0:case 15:return si(t.type,!1);case 11:return si(t.type.render,!1);case 1:return si(t.type,!0);case 31:return Fe("Activity");default:return""}}function Vf(t){try{var l="";do l+=_m(t),t=t.return;while(t);return l}catch(e){return`
Error generating stack: `+e.message+`
`+e.stack}}function yl(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Lf(t){var l=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(l==="checkbox"||l==="radio")}function Om(t){var l=Lf(t)?"checked":"value",e=Object.getOwnPropertyDescriptor(t.constructor.prototype,l),a=""+t[l];if(!t.hasOwnProperty(l)&&typeof e<"u"&&typeof e.get=="function"&&typeof e.set=="function"){var n=e.get,u=e.set;return Object.defineProperty(t,l,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,l,{enumerable:e.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[l]}}}}function Xn(t){t._valueTracker||(t._valueTracker=Om(t))}function Kf(t){if(!t)return!1;var l=t._valueTracker;if(!l)return!0;var e=l.getValue(),a="";return t&&(a=Lf(t)?t.checked?"true":"false":t.value),t=a,t!==e?(l.setValue(t),!0):!1}function Qn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Dm=/[\n"\\]/g;function gl(t){return t.replace(Dm,function(l){return"\\"+l.charCodeAt(0).toString(16)+" "})}function oi(t,l,e,a,n,u,i,c){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),l!=null?i==="number"?(l===0&&t.value===""||t.value!=l)&&(t.value=""+yl(l)):t.value!==""+yl(l)&&(t.value=""+yl(l)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),l!=null?ri(t,i,yl(l)):e!=null?ri(t,i,yl(e)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?t.name=""+yl(c):t.removeAttribute("name")}function Jf(t,l,e,a,n,u,i,c){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),l!=null||e!=null){if(!(u!=="submit"&&u!=="reset"||l!=null))return;e=e!=null?""+yl(e):"",l=l!=null?""+yl(l):e,c||l===t.value||(t.value=l),t.defaultValue=l}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=c?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function ri(t,l,e){l==="number"&&Qn(t.ownerDocument)===t||t.defaultValue===""+e||(t.defaultValue=""+e)}function Pe(t,l,e,a){if(t=t.options,l){l={};for(var n=0;n<e.length;n++)l["$"+e[n]]=!0;for(e=0;e<t.length;e++)n=l.hasOwnProperty("$"+t[e].value),t[e].selected!==n&&(t[e].selected=n),n&&a&&(t[e].defaultSelected=!0)}else{for(e=""+yl(e),l=null,n=0;n<t.length;n++){if(t[n].value===e){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}l!==null||t[n].disabled||(l=t[n])}l!==null&&(l.selected=!0)}}function $f(t,l,e){if(l!=null&&(l=""+yl(l),l!==t.value&&(t.value=l),e==null)){t.defaultValue!==l&&(t.defaultValue=l);return}t.defaultValue=e!=null?""+yl(e):""}function Wf(t,l,e,a){if(l==null){if(a!=null){if(e!=null)throw Error(s(92));if(Tt(a)){if(1<a.length)throw Error(s(93));a=a[0]}e=a}e==null&&(e=""),l=e}e=yl(l),t.defaultValue=e,a=t.textContent,a===e&&a!==""&&a!==null&&(t.value=a)}function Ie(t,l){if(l){var e=t.firstChild;if(e&&e===t.lastChild&&e.nodeType===3){e.nodeValue=l;return}}t.textContent=l}var Nm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ff(t,l,e){var a=l.indexOf("--")===0;e==null||typeof e=="boolean"||e===""?a?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="":a?t.setProperty(l,e):typeof e!="number"||e===0||Nm.has(l)?l==="float"?t.cssFloat=e:t[l]=(""+e).trim():t[l]=e+"px"}function Pf(t,l,e){if(l!=null&&typeof l!="object")throw Error(s(62));if(t=t.style,e!=null){for(var a in e)!e.hasOwnProperty(a)||l!=null&&l.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in l)a=l[n],l.hasOwnProperty(n)&&e[n]!==a&&Ff(t,n,a)}else for(var u in l)l.hasOwnProperty(u)&&Ff(t,u,l[u])}function di(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Um=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function kn(t){return Um.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var mi=null;function hi(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ta=null,la=null;function If(t){var l=Je(t);if(l&&(t=l.stateNode)){var e=t[tl]||null;t:switch(t=l.stateNode,l.type){case"input":if(oi(t,e.value,e.defaultValue,e.defaultValue,e.checked,e.defaultChecked,e.type,e.name),l=e.name,e.type==="radio"&&l!=null){for(e=t;e.parentNode;)e=e.parentNode;for(e=e.querySelectorAll('input[name="'+gl(""+l)+'"][type="radio"]'),l=0;l<e.length;l++){var a=e[l];if(a!==t&&a.form===t.form){var n=a[tl]||null;if(!n)throw Error(s(90));oi(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(l=0;l<e.length;l++)a=e[l],a.form===t.form&&Kf(a)}break t;case"textarea":$f(t,e.value,e.defaultValue);break t;case"select":l=e.value,l!=null&&Pe(t,!!e.multiple,l,!1)}}}var vi=!1;function ts(t,l,e){if(vi)return t(l,e);vi=!0;try{var a=t(l);return a}finally{if(vi=!1,(ta!==null||la!==null)&&(Ou(),ta&&(l=ta,t=la,la=ta=null,If(l),t)))for(l=0;l<t.length;l++)If(t[l])}}function Ya(t,l){var e=t.stateNode;if(e===null)return null;var a=e[tl]||null;if(a===null)return null;e=a[l];t:switch(l){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(e&&typeof e!="function")throw Error(s(231,l,typeof e));return e}var ql=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yi=!1;if(ql)try{var Ga={};Object.defineProperty(Ga,"passive",{get:function(){yi=!0}}),window.addEventListener("test",Ga,Ga),window.removeEventListener("test",Ga,Ga)}catch{yi=!1}var ae=null,gi=null,Zn=null;function ls(){if(Zn)return Zn;var t,l=gi,e=l.length,a,n="value"in ae?ae.value:ae.textContent,u=n.length;for(t=0;t<e&&l[t]===n[t];t++);var i=e-t;for(a=1;a<=i&&l[e-a]===n[u-a];a++);return Zn=n.slice(t,1<a?1-a:void 0)}function Vn(t){var l=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&l===13&&(t=13)):t=l,t===10&&(t=13),32<=t||t===13?t:0}function Ln(){return!0}function es(){return!1}function ll(t){function l(e,a,n,u,i){this._reactName=e,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var c in t)t.hasOwnProperty(c)&&(e=t[c],this[c]=e?e(u):u[c]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Ln:es,this.isPropagationStopped=es,this}return N(l.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():typeof e.returnValue!="unknown"&&(e.returnValue=!1),this.isDefaultPrevented=Ln)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():typeof e.cancelBubble!="unknown"&&(e.cancelBubble=!0),this.isPropagationStopped=Ln)},persist:function(){},isPersistent:Ln}),l}var Oe={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Kn=ll(Oe),Xa=N({},Oe,{view:0,detail:0}),jm=ll(Xa),bi,pi,Qa,Jn=N({},Xa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xi,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Qa&&(Qa&&t.type==="mousemove"?(bi=t.screenX-Qa.screenX,pi=t.screenY-Qa.screenY):pi=bi=0,Qa=t),bi)},movementY:function(t){return"movementY"in t?t.movementY:pi}}),as=ll(Jn),Hm=N({},Jn,{dataTransfer:0}),Cm=ll(Hm),qm=N({},Xa,{relatedTarget:0}),Si=ll(qm),Bm=N({},Oe,{animationName:0,elapsedTime:0,pseudoElement:0}),wm=ll(Bm),Ym=N({},Oe,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Gm=ll(Ym),Xm=N({},Oe,{data:0}),ns=ll(Xm),Qm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},km={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Zm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vm(t){var l=this.nativeEvent;return l.getModifierState?l.getModifierState(t):(t=Zm[t])?!!l[t]:!1}function xi(){return Vm}var Lm=N({},Xa,{key:function(t){if(t.key){var l=Qm[t.key]||t.key;if(l!=="Unidentified")return l}return t.type==="keypress"?(t=Vn(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?km[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xi,charCode:function(t){return t.type==="keypress"?Vn(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Vn(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Km=ll(Lm),Jm=N({},Jn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),us=ll(Jm),$m=N({},Xa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xi}),Wm=ll($m),Fm=N({},Oe,{propertyName:0,elapsedTime:0,pseudoElement:0}),Pm=ll(Fm),Im=N({},Jn,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),th=ll(Im),lh=N({},Oe,{newState:0,oldState:0}),eh=ll(lh),ah=[9,13,27,32],Ai=ql&&"CompositionEvent"in window,ka=null;ql&&"documentMode"in document&&(ka=document.documentMode);var nh=ql&&"TextEvent"in window&&!ka,is=ql&&(!Ai||ka&&8<ka&&11>=ka),cs=" ",fs=!1;function ss(t,l){switch(t){case"keyup":return ah.indexOf(l.keyCode)!==-1;case"keydown":return l.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function os(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ea=!1;function uh(t,l){switch(t){case"compositionend":return os(l);case"keypress":return l.which!==32?null:(fs=!0,cs);case"textInput":return t=l.data,t===cs&&fs?null:t;default:return null}}function ih(t,l){if(ea)return t==="compositionend"||!Ai&&ss(t,l)?(t=ls(),Zn=gi=ae=null,ea=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(l.ctrlKey||l.altKey||l.metaKey)||l.ctrlKey&&l.altKey){if(l.char&&1<l.char.length)return l.char;if(l.which)return String.fromCharCode(l.which)}return null;case"compositionend":return is&&l.locale!=="ko"?null:l.data;default:return null}}var ch={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function rs(t){var l=t&&t.nodeName&&t.nodeName.toLowerCase();return l==="input"?!!ch[t.type]:l==="textarea"}function ds(t,l,e,a){ta?la?la.push(a):la=[a]:ta=a,l=Hu(l,"onChange"),0<l.length&&(e=new Kn("onChange","change",null,e,a),t.push({event:e,listeners:l}))}var Za=null,Va=null;function fh(t){Kr(t,0)}function $n(t){var l=wa(t);if(Kf(l))return t}function ms(t,l){if(t==="change")return l}var hs=!1;if(ql){var Ei;if(ql){var Ti="oninput"in document;if(!Ti){var vs=document.createElement("div");vs.setAttribute("oninput","return;"),Ti=typeof vs.oninput=="function"}Ei=Ti}else Ei=!1;hs=Ei&&(!document.documentMode||9<document.documentMode)}function ys(){Za&&(Za.detachEvent("onpropertychange",gs),Va=Za=null)}function gs(t){if(t.propertyName==="value"&&$n(Va)){var l=[];ds(l,Va,t,hi(t)),ts(fh,l)}}function sh(t,l,e){t==="focusin"?(ys(),Za=l,Va=e,Za.attachEvent("onpropertychange",gs)):t==="focusout"&&ys()}function oh(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return $n(Va)}function rh(t,l){if(t==="click")return $n(l)}function dh(t,l){if(t==="input"||t==="change")return $n(l)}function mh(t,l){return t===l&&(t!==0||1/t===1/l)||t!==t&&l!==l}var sl=typeof Object.is=="function"?Object.is:mh;function La(t,l){if(sl(t,l))return!0;if(typeof t!="object"||t===null||typeof l!="object"||l===null)return!1;var e=Object.keys(t),a=Object.keys(l);if(e.length!==a.length)return!1;for(a=0;a<e.length;a++){var n=e[a];if(!te.call(l,n)||!sl(t[n],l[n]))return!1}return!0}function bs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ps(t,l){var e=bs(t);t=0;for(var a;e;){if(e.nodeType===3){if(a=t+e.textContent.length,t<=l&&a>=l)return{node:e,offset:l-t};t=a}t:{for(;e;){if(e.nextSibling){e=e.nextSibling;break t}e=e.parentNode}e=void 0}e=bs(e)}}function Ss(t,l){return t&&l?t===l?!0:t&&t.nodeType===3?!1:l&&l.nodeType===3?Ss(t,l.parentNode):"contains"in t?t.contains(l):t.compareDocumentPosition?!!(t.compareDocumentPosition(l)&16):!1:!1}function xs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var l=Qn(t.document);l instanceof t.HTMLIFrameElement;){try{var e=typeof l.contentWindow.location.href=="string"}catch{e=!1}if(e)t=l.contentWindow;else break;l=Qn(t.document)}return l}function zi(t){var l=t&&t.nodeName&&t.nodeName.toLowerCase();return l&&(l==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||l==="textarea"||t.contentEditable==="true")}var hh=ql&&"documentMode"in document&&11>=document.documentMode,aa=null,Mi=null,Ka=null,_i=!1;function As(t,l,e){var a=e.window===e?e.document:e.nodeType===9?e:e.ownerDocument;_i||aa==null||aa!==Qn(a)||(a=aa,"selectionStart"in a&&zi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Ka&&La(Ka,a)||(Ka=a,a=Hu(Mi,"onSelect"),0<a.length&&(l=new Kn("onSelect","select",null,l,e),t.push({event:l,listeners:a}),l.target=aa)))}function De(t,l){var e={};return e[t.toLowerCase()]=l.toLowerCase(),e["Webkit"+t]="webkit"+l,e["Moz"+t]="moz"+l,e}var na={animationend:De("Animation","AnimationEnd"),animationiteration:De("Animation","AnimationIteration"),animationstart:De("Animation","AnimationStart"),transitionrun:De("Transition","TransitionRun"),transitionstart:De("Transition","TransitionStart"),transitioncancel:De("Transition","TransitionCancel"),transitionend:De("Transition","TransitionEnd")},Oi={},Es={};ql&&(Es=document.createElement("div").style,"AnimationEvent"in window||(delete na.animationend.animation,delete na.animationiteration.animation,delete na.animationstart.animation),"TransitionEvent"in window||delete na.transitionend.transition);function Ne(t){if(Oi[t])return Oi[t];if(!na[t])return t;var l=na[t],e;for(e in l)if(l.hasOwnProperty(e)&&e in Es)return Oi[t]=l[e];return t}var Ts=Ne("animationend"),zs=Ne("animationiteration"),Ms=Ne("animationstart"),vh=Ne("transitionrun"),yh=Ne("transitionstart"),gh=Ne("transitioncancel"),_s=Ne("transitionend"),Os=new Map,Di="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Di.push("scrollEnd");function Ml(t,l){Os.set(t,l),_e(l,[t])}var Ds=new WeakMap;function bl(t,l){if(typeof t=="object"&&t!==null){var e=Ds.get(t);return e!==void 0?e:(l={value:t,source:l,stack:Vf(l)},Ds.set(t,l),l)}return{value:t,source:l,stack:Vf(l)}}var pl=[],ua=0,Ni=0;function Wn(){for(var t=ua,l=Ni=ua=0;l<t;){var e=pl[l];pl[l++]=null;var a=pl[l];pl[l++]=null;var n=pl[l];pl[l++]=null;var u=pl[l];if(pl[l++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Ns(e,n,u)}}function Fn(t,l,e,a){pl[ua++]=t,pl[ua++]=l,pl[ua++]=e,pl[ua++]=a,Ni|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Ri(t,l,e,a){return Fn(t,l,e,a),Pn(t)}function ia(t,l){return Fn(t,null,null,l),Pn(t)}function Ns(t,l,e){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e);for(var n=!1,u=t.return;u!==null;)u.childLanes|=e,a=u.alternate,a!==null&&(a.childLanes|=e),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&l!==null&&(n=31-fl(e),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[l]:a.push(l),l.lane=e|536870912),u):null}function Pn(t){if(50<pn)throw pn=0,Bc=null,Error(s(185));for(var l=t.return;l!==null;)t=l,l=t.return;return t.tag===3?t.stateNode:null}var ca={};function bh(t,l,e,a){this.tag=t,this.key=e,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=l,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ol(t,l,e,a){return new bh(t,l,e,a)}function Ui(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Bl(t,l){var e=t.alternate;return e===null?(e=ol(t.tag,l,t.key,t.mode),e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.alternate=t,t.alternate=e):(e.pendingProps=l,e.type=t.type,e.flags=0,e.subtreeFlags=0,e.deletions=null),e.flags=t.flags&65011712,e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,l=t.dependencies,e.dependencies=l===null?null:{lanes:l.lanes,firstContext:l.firstContext},e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.refCleanup=t.refCleanup,e}function Rs(t,l){t.flags&=65011714;var e=t.alternate;return e===null?(t.childLanes=0,t.lanes=l,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,t.type=e.type,l=e.dependencies,t.dependencies=l===null?null:{lanes:l.lanes,firstContext:l.firstContext}),t}function In(t,l,e,a,n,u){var i=0;if(a=t,typeof t=="function")Ui(t)&&(i=1);else if(typeof t=="string")i=S0(t,e,B.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Mt:return t=ol(31,e,l,n),t.elementType=Mt,t.lanes=u,t;case P:return Re(e.children,n,u,l);case nt:i=8,n|=24;break;case ot:return t=ol(12,e,l,n|2),t.elementType=ot,t.lanes=u,t;case F:return t=ol(13,e,l,n),t.elementType=F,t.lanes=u,t;case Et:return t=ol(19,e,l,n),t.elementType=Et,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case L:case et:i=10;break t;case rt:i=9;break t;case _t:i=11;break t;case Rt:i=14;break t;case k:i=16,a=null;break t}i=29,e=Error(s(130,t===null?"null":typeof t,"")),a=null}return l=ol(i,e,l,n),l.elementType=t,l.type=a,l.lanes=u,l}function Re(t,l,e,a){return t=ol(7,t,a,l),t.lanes=e,t}function ji(t,l,e){return t=ol(6,t,null,l),t.lanes=e,t}function Hi(t,l,e){return l=ol(4,t.children!==null?t.children:[],t.key,l),l.lanes=e,l.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},l}var fa=[],sa=0,tu=null,lu=0,Sl=[],xl=0,Ue=null,wl=1,Yl="";function je(t,l){fa[sa++]=lu,fa[sa++]=tu,tu=t,lu=l}function Us(t,l,e){Sl[xl++]=wl,Sl[xl++]=Yl,Sl[xl++]=Ue,Ue=t;var a=wl;t=Yl;var n=32-fl(a)-1;a&=~(1<<n),e+=1;var u=32-fl(l)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,wl=1<<32-fl(l)+n|e<<n|a,Yl=u+t}else wl=1<<u|e<<n|a,Yl=t}function Ci(t){t.return!==null&&(je(t,1),Us(t,1,0))}function qi(t){for(;t===tu;)tu=fa[--sa],fa[sa]=null,lu=fa[--sa],fa[sa]=null;for(;t===Ue;)Ue=Sl[--xl],Sl[xl]=null,Yl=Sl[--xl],Sl[xl]=null,wl=Sl[--xl],Sl[xl]=null}var Ft=null,Ut=null,ht=!1,He=null,Nl=!1,Bi=Error(s(519));function Ce(t){var l=Error(s(418,""));throw Wa(bl(l,t)),Bi}function js(t){var l=t.stateNode,e=t.type,a=t.memoizedProps;switch(l[Jt]=t,l[tl]=a,e){case"dialog":ct("cancel",l),ct("close",l);break;case"iframe":case"object":case"embed":ct("load",l);break;case"video":case"audio":for(e=0;e<xn.length;e++)ct(xn[e],l);break;case"source":ct("error",l);break;case"img":case"image":case"link":ct("error",l),ct("load",l);break;case"details":ct("toggle",l);break;case"input":ct("invalid",l),Jf(l,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Xn(l);break;case"select":ct("invalid",l);break;case"textarea":ct("invalid",l),Wf(l,a.value,a.defaultValue,a.children),Xn(l)}e=a.children,typeof e!="string"&&typeof e!="number"&&typeof e!="bigint"||l.textContent===""+e||a.suppressHydrationWarning===!0||Fr(l.textContent,e)?(a.popover!=null&&(ct("beforetoggle",l),ct("toggle",l)),a.onScroll!=null&&ct("scroll",l),a.onScrollEnd!=null&&ct("scrollend",l),a.onClick!=null&&(l.onclick=Cu),l=!0):l=!1,l||Ce(t)}function Hs(t){for(Ft=t.return;Ft;)switch(Ft.tag){case 5:case 13:Nl=!1;return;case 27:case 3:Nl=!0;return;default:Ft=Ft.return}}function Ja(t){if(t!==Ft)return!1;if(!ht)return Hs(t),ht=!0,!1;var l=t.tag,e;if((e=l!==3&&l!==27)&&((e=l===5)&&(e=t.type,e=!(e!=="form"&&e!=="button")||Ic(t.type,t.memoizedProps)),e=!e),e&&Ut&&Ce(t),Hs(t),l===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,l=0;t;){if(t.nodeType===8)if(e=t.data,e==="/$"){if(l===0){Ut=Ol(t.nextSibling);break t}l--}else e!=="$"&&e!=="$!"&&e!=="$?"||l++;t=t.nextSibling}Ut=null}}else l===27?(l=Ut,pe(t.type)?(t=af,af=null,Ut=t):Ut=l):Ut=Ft?Ol(t.stateNode.nextSibling):null;return!0}function $a(){Ut=Ft=null,ht=!1}function Cs(){var t=He;return t!==null&&(nl===null?nl=t:nl.push.apply(nl,t),He=null),t}function Wa(t){He===null?He=[t]:He.push(t)}var wi=D(null),qe=null,Gl=null;function ne(t,l,e){j(wi,l._currentValue),l._currentValue=e}function Xl(t){t._currentValue=wi.current,q(wi)}function Yi(t,l,e){for(;t!==null;){var a=t.alternate;if((t.childLanes&l)!==l?(t.childLanes|=l,a!==null&&(a.childLanes|=l)):a!==null&&(a.childLanes&l)!==l&&(a.childLanes|=l),t===e)break;t=t.return}}function Gi(t,l,e,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var c=u;u=n;for(var o=0;o<l.length;o++)if(c.context===l[o]){u.lanes|=e,c=u.alternate,c!==null&&(c.lanes|=e),Yi(u.return,e,t),a||(i=null);break t}u=c.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(s(341));i.lanes|=e,u=i.alternate,u!==null&&(u.lanes|=e),Yi(i,e,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function Fa(t,l,e,a){t=null;for(var n=l,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(s(387));if(i=i.memoizedProps,i!==null){var c=n.type;sl(n.pendingProps.value,i.value)||(t!==null?t.push(c):t=[c])}}else if(n===dt.current){if(i=n.alternate,i===null)throw Error(s(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(_n):t=[_n])}n=n.return}t!==null&&Gi(l,t,e,a),l.flags|=262144}function eu(t){for(t=t.firstContext;t!==null;){if(!sl(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Be(t){qe=t,Gl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function $t(t){return qs(qe,t)}function au(t,l){return qe===null&&Be(t),qs(t,l)}function qs(t,l){var e=l._currentValue;if(l={context:l,memoizedValue:e,next:null},Gl===null){if(t===null)throw Error(s(308));Gl=l,t.dependencies={lanes:0,firstContext:l},t.flags|=524288}else Gl=Gl.next=l;return e}var ph=typeof AbortController<"u"?AbortController:function(){var t=[],l=this.signal={aborted:!1,addEventListener:function(e,a){t.push(a)}};this.abort=function(){l.aborted=!0,t.forEach(function(e){return e()})}},Sh=f.unstable_scheduleCallback,xh=f.unstable_NormalPriority,wt={$$typeof:et,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Xi(){return{controller:new ph,data:new Map,refCount:0}}function Pa(t){t.refCount--,t.refCount===0&&Sh(xh,function(){t.controller.abort()})}var Ia=null,Qi=0,oa=0,ra=null;function Ah(t,l){if(Ia===null){var e=Ia=[];Qi=0,oa=Zc(),ra={status:"pending",value:void 0,then:function(a){e.push(a)}}}return Qi++,l.then(Bs,Bs),l}function Bs(){if(--Qi===0&&Ia!==null){ra!==null&&(ra.status="fulfilled");var t=Ia;Ia=null,oa=0,ra=null;for(var l=0;l<t.length;l++)(0,t[l])()}}function Eh(t,l){var e=[],a={status:"pending",value:null,reason:null,then:function(n){e.push(n)}};return t.then(function(){a.status="fulfilled",a.value=l;for(var n=0;n<e.length;n++)(0,e[n])(l)},function(n){for(a.status="rejected",a.reason=n,n=0;n<e.length;n++)(0,e[n])(void 0)}),a}var ws=T.S;T.S=function(t,l){typeof l=="object"&&l!==null&&typeof l.then=="function"&&Ah(t,l),ws!==null&&ws(t,l)};var we=D(null);function ki(){var t=we.current;return t!==null?t:zt.pooledCache}function nu(t,l){l===null?j(we,we.current):j(we,l.pool)}function Ys(){var t=ki();return t===null?null:{parent:wt._currentValue,pool:t}}var tn=Error(s(460)),Gs=Error(s(474)),uu=Error(s(542)),Zi={then:function(){}};function Xs(t){return t=t.status,t==="fulfilled"||t==="rejected"}function iu(){}function Qs(t,l,e){switch(e=t[e],e===void 0?t.push(l):e!==l&&(l.then(iu,iu),l=e),l.status){case"fulfilled":return l.value;case"rejected":throw t=l.reason,Zs(t),t;default:if(typeof l.status=="string")l.then(iu,iu);else{if(t=zt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=l,t.status="pending",t.then(function(a){if(l.status==="pending"){var n=l;n.status="fulfilled",n.value=a}},function(a){if(l.status==="pending"){var n=l;n.status="rejected",n.reason=a}})}switch(l.status){case"fulfilled":return l.value;case"rejected":throw t=l.reason,Zs(t),t}throw ln=l,tn}}var ln=null;function ks(){if(ln===null)throw Error(s(459));var t=ln;return ln=null,t}function Zs(t){if(t===tn||t===uu)throw Error(s(483))}var ue=!1;function Vi(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Li(t,l){t=t.updateQueue,l.updateQueue===t&&(l.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function ie(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ce(t,l,e){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(yt&2)!==0){var n=a.pending;return n===null?l.next=l:(l.next=n.next,n.next=l),a.pending=l,l=Pn(t),Ns(t,null,e),l}return Fn(t,a,l,e),Pn(t)}function en(t,l,e){if(l=l.updateQueue,l!==null&&(l=l.shared,(e&4194048)!==0)){var a=l.lanes;a&=t.pendingLanes,e|=a,l.lanes=e,Bf(t,e)}}function Ki(t,l){var e=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,e===a)){var n=null,u=null;if(e=e.firstBaseUpdate,e!==null){do{var i={lane:e.lane,tag:e.tag,payload:e.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,e=e.next}while(e!==null);u===null?n=u=l:u=u.next=l}else n=u=l;e={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=e;return}t=e.lastBaseUpdate,t===null?e.firstBaseUpdate=l:t.next=l,e.lastBaseUpdate=l}var Ji=!1;function an(){if(Ji){var t=ra;if(t!==null)throw t}}function nn(t,l,e,a){Ji=!1;var n=t.updateQueue;ue=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,c=n.shared.pending;if(c!==null){n.shared.pending=null;var o=c,b=o.next;o.next=null,i===null?u=b:i.next=b,i=o;var z=t.alternate;z!==null&&(z=z.updateQueue,c=z.lastBaseUpdate,c!==i&&(c===null?z.firstBaseUpdate=b:c.next=b,z.lastBaseUpdate=o))}if(u!==null){var O=n.baseState;i=0,z=b=o=null,c=u;do{var S=c.lane&-536870913,x=S!==c.lane;if(x?(st&S)===S:(a&S)===S){S!==0&&S===oa&&(Ji=!0),z!==null&&(z=z.next={lane:0,tag:c.tag,payload:c.payload,callback:null,next:null});t:{var $=t,K=c;S=l;var St=e;switch(K.tag){case 1:if($=K.payload,typeof $=="function"){O=$.call(St,O,S);break t}O=$;break t;case 3:$.flags=$.flags&-65537|128;case 0:if($=K.payload,S=typeof $=="function"?$.call(St,O,S):$,S==null)break t;O=N({},O,S);break t;case 2:ue=!0}}S=c.callback,S!==null&&(t.flags|=64,x&&(t.flags|=8192),x=n.callbacks,x===null?n.callbacks=[S]:x.push(S))}else x={lane:S,tag:c.tag,payload:c.payload,callback:c.callback,next:null},z===null?(b=z=x,o=O):z=z.next=x,i|=S;if(c=c.next,c===null){if(c=n.shared.pending,c===null)break;x=c,c=x.next,x.next=null,n.lastBaseUpdate=x,n.shared.pending=null}}while(!0);z===null&&(o=O),n.baseState=o,n.firstBaseUpdate=b,n.lastBaseUpdate=z,u===null&&(n.shared.lanes=0),ve|=i,t.lanes=i,t.memoizedState=O}}function Vs(t,l){if(typeof t!="function")throw Error(s(191,t));t.call(l)}function Ls(t,l){var e=t.callbacks;if(e!==null)for(t.callbacks=null,t=0;t<e.length;t++)Vs(e[t],l)}var da=D(null),cu=D(0);function Ks(t,l){t=Jl,j(cu,t),j(da,l),Jl=t|l.baseLanes}function $i(){j(cu,Jl),j(da,da.current)}function Wi(){Jl=cu.current,q(da),q(cu)}var fe=0,tt=null,bt=null,qt=null,fu=!1,ma=!1,Ye=!1,su=0,un=0,ha=null,Th=0;function Ht(){throw Error(s(321))}function Fi(t,l){if(l===null)return!1;for(var e=0;e<l.length&&e<t.length;e++)if(!sl(t[e],l[e]))return!1;return!0}function Pi(t,l,e,a,n,u){return fe=u,tt=l,l.memoizedState=null,l.updateQueue=null,l.lanes=0,T.H=t===null||t.memoizedState===null?Ro:Uo,Ye=!1,u=e(a,n),Ye=!1,ma&&(u=$s(l,e,a,n)),Js(t),u}function Js(t){T.H=vu;var l=bt!==null&&bt.next!==null;if(fe=0,qt=bt=tt=null,fu=!1,un=0,ha=null,l)throw Error(s(300));t===null||Qt||(t=t.dependencies,t!==null&&eu(t)&&(Qt=!0))}function $s(t,l,e,a){tt=t;var n=0;do{if(ma&&(ha=null),un=0,ma=!1,25<=n)throw Error(s(301));if(n+=1,qt=bt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}T.H=Rh,u=l(e,a)}while(ma);return u}function zh(){var t=T.H,l=t.useState()[0];return l=typeof l.then=="function"?cn(l):l,t=t.useState()[0],(bt!==null?bt.memoizedState:null)!==t&&(tt.flags|=1024),l}function Ii(){var t=su!==0;return su=0,t}function tc(t,l,e){l.updateQueue=t.updateQueue,l.flags&=-2053,t.lanes&=~e}function lc(t){if(fu){for(t=t.memoizedState;t!==null;){var l=t.queue;l!==null&&(l.pending=null),t=t.next}fu=!1}fe=0,qt=bt=tt=null,ma=!1,un=su=0,ha=null}function el(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qt===null?tt.memoizedState=qt=t:qt=qt.next=t,qt}function Bt(){if(bt===null){var t=tt.alternate;t=t!==null?t.memoizedState:null}else t=bt.next;var l=qt===null?tt.memoizedState:qt.next;if(l!==null)qt=l,bt=t;else{if(t===null)throw tt.alternate===null?Error(s(467)):Error(s(310));bt=t,t={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},qt===null?tt.memoizedState=qt=t:qt=qt.next=t}return qt}function ec(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function cn(t){var l=un;return un+=1,ha===null&&(ha=[]),t=Qs(ha,t,l),l=tt,(qt===null?l.memoizedState:qt.next)===null&&(l=l.alternate,T.H=l===null||l.memoizedState===null?Ro:Uo),t}function ou(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return cn(t);if(t.$$typeof===et)return $t(t)}throw Error(s(438,String(t)))}function ac(t){var l=null,e=tt.updateQueue;if(e!==null&&(l=e.memoCache),l==null){var a=tt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(l={data:a.data.map(function(n){return n.slice()}),index:0})))}if(l==null&&(l={data:[],index:0}),e===null&&(e=ec(),tt.updateQueue=e),e.memoCache=l,e=l.data[l.index],e===void 0)for(e=l.data[l.index]=Array(t),a=0;a<t;a++)e[a]=zl;return l.index++,e}function Ql(t,l){return typeof l=="function"?l(t):l}function ru(t){var l=Bt();return nc(l,bt,t)}function nc(t,l,e){var a=t.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=e;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}l.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{l=n.next;var c=i=null,o=null,b=l,z=!1;do{var O=b.lane&-536870913;if(O!==b.lane?(st&O)===O:(fe&O)===O){var S=b.revertLane;if(S===0)o!==null&&(o=o.next={lane:0,revertLane:0,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null}),O===oa&&(z=!0);else if((fe&S)===S){b=b.next,S===oa&&(z=!0);continue}else O={lane:0,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},o===null?(c=o=O,i=u):o=o.next=O,tt.lanes|=S,ve|=S;O=b.action,Ye&&e(u,O),u=b.hasEagerState?b.eagerState:e(u,O)}else S={lane:O,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},o===null?(c=o=S,i=u):o=o.next=S,tt.lanes|=O,ve|=O;b=b.next}while(b!==null&&b!==l);if(o===null?i=u:o.next=c,!sl(u,t.memoizedState)&&(Qt=!0,z&&(e=ra,e!==null)))throw e;t.memoizedState=u,t.baseState=i,t.baseQueue=o,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function uc(t){var l=Bt(),e=l.queue;if(e===null)throw Error(s(311));e.lastRenderedReducer=t;var a=e.dispatch,n=e.pending,u=l.memoizedState;if(n!==null){e.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);sl(u,l.memoizedState)||(Qt=!0),l.memoizedState=u,l.baseQueue===null&&(l.baseState=u),e.lastRenderedState=u}return[u,a]}function Ws(t,l,e){var a=tt,n=Bt(),u=ht;if(u){if(e===void 0)throw Error(s(407));e=e()}else e=l();var i=!sl((bt||n).memoizedState,e);i&&(n.memoizedState=e,Qt=!0),n=n.queue;var c=Is.bind(null,a,n,t);if(fn(2048,8,c,[t]),n.getSnapshot!==l||i||qt!==null&&qt.memoizedState.tag&1){if(a.flags|=2048,va(9,du(),Ps.bind(null,a,n,e,l),null),zt===null)throw Error(s(349));u||(fe&124)!==0||Fs(a,l,e)}return e}function Fs(t,l,e){t.flags|=16384,t={getSnapshot:l,value:e},l=tt.updateQueue,l===null?(l=ec(),tt.updateQueue=l,l.stores=[t]):(e=l.stores,e===null?l.stores=[t]:e.push(t))}function Ps(t,l,e,a){l.value=e,l.getSnapshot=a,to(l)&&lo(t)}function Is(t,l,e){return e(function(){to(l)&&lo(t)})}function to(t){var l=t.getSnapshot;t=t.value;try{var e=l();return!sl(t,e)}catch{return!0}}function lo(t){var l=ia(t,2);l!==null&&vl(l,t,2)}function ic(t){var l=el();if(typeof t=="function"){var e=t;if(t=e(),Ye){le(!0);try{e()}finally{le(!1)}}}return l.memoizedState=l.baseState=t,l.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:t},l}function eo(t,l,e,a){return t.baseState=e,nc(t,bt,typeof a=="function"?a:Ql)}function Mh(t,l,e,a,n){if(hu(t))throw Error(s(485));if(t=l.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};T.T!==null?e(!0):u.isTransition=!1,a(u),e=l.pending,e===null?(u.next=l.pending=u,ao(l,u)):(u.next=e.next,l.pending=e.next=u)}}function ao(t,l){var e=l.action,a=l.payload,n=t.state;if(l.isTransition){var u=T.T,i={};T.T=i;try{var c=e(n,a),o=T.S;o!==null&&o(i,c),no(t,l,c)}catch(b){cc(t,l,b)}finally{T.T=u}}else try{u=e(n,a),no(t,l,u)}catch(b){cc(t,l,b)}}function no(t,l,e){e!==null&&typeof e=="object"&&typeof e.then=="function"?e.then(function(a){uo(t,l,a)},function(a){return cc(t,l,a)}):uo(t,l,e)}function uo(t,l,e){l.status="fulfilled",l.value=e,io(l),t.state=e,l=t.pending,l!==null&&(e=l.next,e===l?t.pending=null:(e=e.next,l.next=e,ao(t,e)))}function cc(t,l,e){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do l.status="rejected",l.reason=e,io(l),l=l.next;while(l!==a)}t.action=null}function io(t){t=t.listeners;for(var l=0;l<t.length;l++)(0,t[l])()}function co(t,l){return l}function fo(t,l){if(ht){var e=zt.formState;if(e!==null){t:{var a=tt;if(ht){if(Ut){l:{for(var n=Ut,u=Nl;n.nodeType!==8;){if(!u){n=null;break l}if(n=Ol(n.nextSibling),n===null){n=null;break l}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ut=Ol(n.nextSibling),a=n.data==="F!";break t}}Ce(a)}a=!1}a&&(l=e[0])}}return e=el(),e.memoizedState=e.baseState=l,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:co,lastRenderedState:l},e.queue=a,e=Oo.bind(null,tt,a),a.dispatch=e,a=ic(!1),u=dc.bind(null,tt,!1,a.queue),a=el(),n={state:l,dispatch:null,action:t,pending:null},a.queue=n,e=Mh.bind(null,tt,n,u,e),n.dispatch=e,a.memoizedState=t,[l,e,!1]}function so(t){var l=Bt();return oo(l,bt,t)}function oo(t,l,e){if(l=nc(t,l,co)[0],t=ru(Ql)[0],typeof l=="object"&&l!==null&&typeof l.then=="function")try{var a=cn(l)}catch(i){throw i===tn?uu:i}else a=l;l=Bt();var n=l.queue,u=n.dispatch;return e!==l.memoizedState&&(tt.flags|=2048,va(9,du(),_h.bind(null,n,e),null)),[a,u,t]}function _h(t,l){t.action=l}function ro(t){var l=Bt(),e=bt;if(e!==null)return oo(l,e,t);Bt(),l=l.memoizedState,e=Bt();var a=e.queue.dispatch;return e.memoizedState=t,[l,a,!1]}function va(t,l,e,a){return t={tag:t,create:e,deps:a,inst:l,next:null},l=tt.updateQueue,l===null&&(l=ec(),tt.updateQueue=l),e=l.lastEffect,e===null?l.lastEffect=t.next=t:(a=e.next,e.next=t,t.next=a,l.lastEffect=t),t}function du(){return{destroy:void 0,resource:void 0}}function mo(){return Bt().memoizedState}function mu(t,l,e,a){var n=el();a=a===void 0?null:a,tt.flags|=t,n.memoizedState=va(1|l,du(),e,a)}function fn(t,l,e,a){var n=Bt();a=a===void 0?null:a;var u=n.memoizedState.inst;bt!==null&&a!==null&&Fi(a,bt.memoizedState.deps)?n.memoizedState=va(l,u,e,a):(tt.flags|=t,n.memoizedState=va(1|l,u,e,a))}function ho(t,l){mu(8390656,8,t,l)}function vo(t,l){fn(2048,8,t,l)}function yo(t,l){return fn(4,2,t,l)}function go(t,l){return fn(4,4,t,l)}function bo(t,l){if(typeof l=="function"){t=t();var e=l(t);return function(){typeof e=="function"?e():l(null)}}if(l!=null)return t=t(),l.current=t,function(){l.current=null}}function po(t,l,e){e=e!=null?e.concat([t]):null,fn(4,4,bo.bind(null,l,t),e)}function fc(){}function So(t,l){var e=Bt();l=l===void 0?null:l;var a=e.memoizedState;return l!==null&&Fi(l,a[1])?a[0]:(e.memoizedState=[t,l],t)}function xo(t,l){var e=Bt();l=l===void 0?null:l;var a=e.memoizedState;if(l!==null&&Fi(l,a[1]))return a[0];if(a=t(),Ye){le(!0);try{t()}finally{le(!1)}}return e.memoizedState=[a,l],a}function sc(t,l,e){return e===void 0||(fe&1073741824)!==0?t.memoizedState=l:(t.memoizedState=e,t=Tr(),tt.lanes|=t,ve|=t,e)}function Ao(t,l,e,a){return sl(e,l)?e:da.current!==null?(t=sc(t,e,a),sl(t,l)||(Qt=!0),t):(fe&42)===0?(Qt=!0,t.memoizedState=e):(t=Tr(),tt.lanes|=t,ve|=t,l)}function Eo(t,l,e,a,n){var u=H.p;H.p=u!==0&&8>u?u:8;var i=T.T,c={};T.T=c,dc(t,!1,l,e);try{var o=n(),b=T.S;if(b!==null&&b(c,o),o!==null&&typeof o=="object"&&typeof o.then=="function"){var z=Eh(o,a);sn(t,l,z,hl(t))}else sn(t,l,a,hl(t))}catch(O){sn(t,l,{then:function(){},status:"rejected",reason:O},hl())}finally{H.p=u,T.T=i}}function Oh(){}function oc(t,l,e,a){if(t.tag!==5)throw Error(s(476));var n=To(t).queue;Eo(t,n,l,R,e===null?Oh:function(){return zo(t),e(a)})}function To(t){var l=t.memoizedState;if(l!==null)return l;l={memoizedState:R,baseState:R,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:R},next:null};var e={};return l.next={memoizedState:e,baseState:e,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:e},next:null},t.memoizedState=l,t=t.alternate,t!==null&&(t.memoizedState=l),l}function zo(t){var l=To(t).next.queue;sn(t,l,{},hl())}function rc(){return $t(_n)}function Mo(){return Bt().memoizedState}function _o(){return Bt().memoizedState}function Dh(t){for(var l=t.return;l!==null;){switch(l.tag){case 24:case 3:var e=hl();t=ie(e);var a=ce(l,t,e);a!==null&&(vl(a,l,e),en(a,l,e)),l={cache:Xi()},t.payload=l;return}l=l.return}}function Nh(t,l,e){var a=hl();e={lane:a,revertLane:0,action:e,hasEagerState:!1,eagerState:null,next:null},hu(t)?Do(l,e):(e=Ri(t,l,e,a),e!==null&&(vl(e,t,a),No(e,l,a)))}function Oo(t,l,e){var a=hl();sn(t,l,e,a)}function sn(t,l,e,a){var n={lane:a,revertLane:0,action:e,hasEagerState:!1,eagerState:null,next:null};if(hu(t))Do(l,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=l.lastRenderedReducer,u!==null))try{var i=l.lastRenderedState,c=u(i,e);if(n.hasEagerState=!0,n.eagerState=c,sl(c,i))return Fn(t,l,n,0),zt===null&&Wn(),!1}catch{}finally{}if(e=Ri(t,l,n,a),e!==null)return vl(e,t,a),No(e,l,a),!0}return!1}function dc(t,l,e,a){if(a={lane:2,revertLane:Zc(),action:a,hasEagerState:!1,eagerState:null,next:null},hu(t)){if(l)throw Error(s(479))}else l=Ri(t,e,a,2),l!==null&&vl(l,t,2)}function hu(t){var l=t.alternate;return t===tt||l!==null&&l===tt}function Do(t,l){ma=fu=!0;var e=t.pending;e===null?l.next=l:(l.next=e.next,e.next=l),t.pending=l}function No(t,l,e){if((e&4194048)!==0){var a=l.lanes;a&=t.pendingLanes,e|=a,l.lanes=e,Bf(t,e)}}var vu={readContext:$t,use:ou,useCallback:Ht,useContext:Ht,useEffect:Ht,useImperativeHandle:Ht,useLayoutEffect:Ht,useInsertionEffect:Ht,useMemo:Ht,useReducer:Ht,useRef:Ht,useState:Ht,useDebugValue:Ht,useDeferredValue:Ht,useTransition:Ht,useSyncExternalStore:Ht,useId:Ht,useHostTransitionStatus:Ht,useFormState:Ht,useActionState:Ht,useOptimistic:Ht,useMemoCache:Ht,useCacheRefresh:Ht},Ro={readContext:$t,use:ou,useCallback:function(t,l){return el().memoizedState=[t,l===void 0?null:l],t},useContext:$t,useEffect:ho,useImperativeHandle:function(t,l,e){e=e!=null?e.concat([t]):null,mu(4194308,4,bo.bind(null,l,t),e)},useLayoutEffect:function(t,l){return mu(4194308,4,t,l)},useInsertionEffect:function(t,l){mu(4,2,t,l)},useMemo:function(t,l){var e=el();l=l===void 0?null:l;var a=t();if(Ye){le(!0);try{t()}finally{le(!1)}}return e.memoizedState=[a,l],a},useReducer:function(t,l,e){var a=el();if(e!==void 0){var n=e(l);if(Ye){le(!0);try{e(l)}finally{le(!1)}}}else n=l;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=Nh.bind(null,tt,t),[a.memoizedState,t]},useRef:function(t){var l=el();return t={current:t},l.memoizedState=t},useState:function(t){t=ic(t);var l=t.queue,e=Oo.bind(null,tt,l);return l.dispatch=e,[t.memoizedState,e]},useDebugValue:fc,useDeferredValue:function(t,l){var e=el();return sc(e,t,l)},useTransition:function(){var t=ic(!1);return t=Eo.bind(null,tt,t.queue,!0,!1),el().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,l,e){var a=tt,n=el();if(ht){if(e===void 0)throw Error(s(407));e=e()}else{if(e=l(),zt===null)throw Error(s(349));(st&124)!==0||Fs(a,l,e)}n.memoizedState=e;var u={value:e,getSnapshot:l};return n.queue=u,ho(Is.bind(null,a,u,t),[t]),a.flags|=2048,va(9,du(),Ps.bind(null,a,u,e,l),null),e},useId:function(){var t=el(),l=zt.identifierPrefix;if(ht){var e=Yl,a=wl;e=(a&~(1<<32-fl(a)-1)).toString(32)+e,l="«"+l+"R"+e,e=su++,0<e&&(l+="H"+e.toString(32)),l+="»"}else e=Th++,l="«"+l+"r"+e.toString(32)+"»";return t.memoizedState=l},useHostTransitionStatus:rc,useFormState:fo,useActionState:fo,useOptimistic:function(t){var l=el();l.memoizedState=l.baseState=t;var e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return l.queue=e,l=dc.bind(null,tt,!0,e),e.dispatch=l,[t,l]},useMemoCache:ac,useCacheRefresh:function(){return el().memoizedState=Dh.bind(null,tt)}},Uo={readContext:$t,use:ou,useCallback:So,useContext:$t,useEffect:vo,useImperativeHandle:po,useInsertionEffect:yo,useLayoutEffect:go,useMemo:xo,useReducer:ru,useRef:mo,useState:function(){return ru(Ql)},useDebugValue:fc,useDeferredValue:function(t,l){var e=Bt();return Ao(e,bt.memoizedState,t,l)},useTransition:function(){var t=ru(Ql)[0],l=Bt().memoizedState;return[typeof t=="boolean"?t:cn(t),l]},useSyncExternalStore:Ws,useId:Mo,useHostTransitionStatus:rc,useFormState:so,useActionState:so,useOptimistic:function(t,l){var e=Bt();return eo(e,bt,t,l)},useMemoCache:ac,useCacheRefresh:_o},Rh={readContext:$t,use:ou,useCallback:So,useContext:$t,useEffect:vo,useImperativeHandle:po,useInsertionEffect:yo,useLayoutEffect:go,useMemo:xo,useReducer:uc,useRef:mo,useState:function(){return uc(Ql)},useDebugValue:fc,useDeferredValue:function(t,l){var e=Bt();return bt===null?sc(e,t,l):Ao(e,bt.memoizedState,t,l)},useTransition:function(){var t=uc(Ql)[0],l=Bt().memoizedState;return[typeof t=="boolean"?t:cn(t),l]},useSyncExternalStore:Ws,useId:Mo,useHostTransitionStatus:rc,useFormState:ro,useActionState:ro,useOptimistic:function(t,l){var e=Bt();return bt!==null?eo(e,bt,t,l):(e.baseState=t,[t,e.queue.dispatch])},useMemoCache:ac,useCacheRefresh:_o},ya=null,on=0;function yu(t){var l=on;return on+=1,ya===null&&(ya=[]),Qs(ya,t,l)}function rn(t,l){l=l.props.ref,t.ref=l!==void 0?l:null}function gu(t,l){throw l.$$typeof===X?Error(s(525)):(t=Object.prototype.toString.call(l),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(l).join(", ")+"}":t)))}function jo(t){var l=t._init;return l(t._payload)}function Ho(t){function l(v,d){if(t){var y=v.deletions;y===null?(v.deletions=[d],v.flags|=16):y.push(d)}}function e(v,d){if(!t)return null;for(;d!==null;)l(v,d),d=d.sibling;return null}function a(v){for(var d=new Map;v!==null;)v.key!==null?d.set(v.key,v):d.set(v.index,v),v=v.sibling;return d}function n(v,d){return v=Bl(v,d),v.index=0,v.sibling=null,v}function u(v,d,y){return v.index=y,t?(y=v.alternate,y!==null?(y=y.index,y<d?(v.flags|=67108866,d):y):(v.flags|=67108866,d)):(v.flags|=1048576,d)}function i(v){return t&&v.alternate===null&&(v.flags|=67108866),v}function c(v,d,y,_){return d===null||d.tag!==6?(d=ji(y,v.mode,_),d.return=v,d):(d=n(d,y),d.return=v,d)}function o(v,d,y,_){var w=y.type;return w===P?z(v,d,y.props.children,_,y.key):d!==null&&(d.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===k&&jo(w)===d.type)?(d=n(d,y.props),rn(d,y),d.return=v,d):(d=In(y.type,y.key,y.props,null,v.mode,_),rn(d,y),d.return=v,d)}function b(v,d,y,_){return d===null||d.tag!==4||d.stateNode.containerInfo!==y.containerInfo||d.stateNode.implementation!==y.implementation?(d=Hi(y,v.mode,_),d.return=v,d):(d=n(d,y.children||[]),d.return=v,d)}function z(v,d,y,_,w){return d===null||d.tag!==7?(d=Re(y,v.mode,_,w),d.return=v,d):(d=n(d,y),d.return=v,d)}function O(v,d,y){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=ji(""+d,v.mode,y),d.return=v,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Q:return y=In(d.type,d.key,d.props,null,v.mode,y),rn(y,d),y.return=v,y;case ft:return d=Hi(d,v.mode,y),d.return=v,d;case k:var _=d._init;return d=_(d._payload),O(v,d,y)}if(Tt(d)||Ot(d))return d=Re(d,v.mode,y,null),d.return=v,d;if(typeof d.then=="function")return O(v,yu(d),y);if(d.$$typeof===et)return O(v,au(v,d),y);gu(v,d)}return null}function S(v,d,y,_){var w=d!==null?d.key:null;if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return w!==null?null:c(v,d,""+y,_);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Q:return y.key===w?o(v,d,y,_):null;case ft:return y.key===w?b(v,d,y,_):null;case k:return w=y._init,y=w(y._payload),S(v,d,y,_)}if(Tt(y)||Ot(y))return w!==null?null:z(v,d,y,_,null);if(typeof y.then=="function")return S(v,d,yu(y),_);if(y.$$typeof===et)return S(v,d,au(v,y),_);gu(v,y)}return null}function x(v,d,y,_,w){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return v=v.get(y)||null,c(d,v,""+_,w);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case Q:return v=v.get(_.key===null?y:_.key)||null,o(d,v,_,w);case ft:return v=v.get(_.key===null?y:_.key)||null,b(d,v,_,w);case k:var at=_._init;return _=at(_._payload),x(v,d,y,_,w)}if(Tt(_)||Ot(_))return v=v.get(y)||null,z(d,v,_,w,null);if(typeof _.then=="function")return x(v,d,y,yu(_),w);if(_.$$typeof===et)return x(v,d,y,au(d,_),w);gu(d,_)}return null}function $(v,d,y,_){for(var w=null,at=null,Z=d,J=d=0,Zt=null;Z!==null&&J<y.length;J++){Z.index>J?(Zt=Z,Z=null):Zt=Z.sibling;var mt=S(v,Z,y[J],_);if(mt===null){Z===null&&(Z=Zt);break}t&&Z&&mt.alternate===null&&l(v,Z),d=u(mt,d,J),at===null?w=mt:at.sibling=mt,at=mt,Z=Zt}if(J===y.length)return e(v,Z),ht&&je(v,J),w;if(Z===null){for(;J<y.length;J++)Z=O(v,y[J],_),Z!==null&&(d=u(Z,d,J),at===null?w=Z:at.sibling=Z,at=Z);return ht&&je(v,J),w}for(Z=a(Z);J<y.length;J++)Zt=x(Z,v,J,y[J],_),Zt!==null&&(t&&Zt.alternate!==null&&Z.delete(Zt.key===null?J:Zt.key),d=u(Zt,d,J),at===null?w=Zt:at.sibling=Zt,at=Zt);return t&&Z.forEach(function(Te){return l(v,Te)}),ht&&je(v,J),w}function K(v,d,y,_){if(y==null)throw Error(s(151));for(var w=null,at=null,Z=d,J=d=0,Zt=null,mt=y.next();Z!==null&&!mt.done;J++,mt=y.next()){Z.index>J?(Zt=Z,Z=null):Zt=Z.sibling;var Te=S(v,Z,mt.value,_);if(Te===null){Z===null&&(Z=Zt);break}t&&Z&&Te.alternate===null&&l(v,Z),d=u(Te,d,J),at===null?w=Te:at.sibling=Te,at=Te,Z=Zt}if(mt.done)return e(v,Z),ht&&je(v,J),w;if(Z===null){for(;!mt.done;J++,mt=y.next())mt=O(v,mt.value,_),mt!==null&&(d=u(mt,d,J),at===null?w=mt:at.sibling=mt,at=mt);return ht&&je(v,J),w}for(Z=a(Z);!mt.done;J++,mt=y.next())mt=x(Z,v,J,mt.value,_),mt!==null&&(t&&mt.alternate!==null&&Z.delete(mt.key===null?J:mt.key),d=u(mt,d,J),at===null?w=mt:at.sibling=mt,at=mt);return t&&Z.forEach(function(U0){return l(v,U0)}),ht&&je(v,J),w}function St(v,d,y,_){if(typeof y=="object"&&y!==null&&y.type===P&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Q:t:{for(var w=y.key;d!==null;){if(d.key===w){if(w=y.type,w===P){if(d.tag===7){e(v,d.sibling),_=n(d,y.props.children),_.return=v,v=_;break t}}else if(d.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===k&&jo(w)===d.type){e(v,d.sibling),_=n(d,y.props),rn(_,y),_.return=v,v=_;break t}e(v,d);break}else l(v,d);d=d.sibling}y.type===P?(_=Re(y.props.children,v.mode,_,y.key),_.return=v,v=_):(_=In(y.type,y.key,y.props,null,v.mode,_),rn(_,y),_.return=v,v=_)}return i(v);case ft:t:{for(w=y.key;d!==null;){if(d.key===w)if(d.tag===4&&d.stateNode.containerInfo===y.containerInfo&&d.stateNode.implementation===y.implementation){e(v,d.sibling),_=n(d,y.children||[]),_.return=v,v=_;break t}else{e(v,d);break}else l(v,d);d=d.sibling}_=Hi(y,v.mode,_),_.return=v,v=_}return i(v);case k:return w=y._init,y=w(y._payload),St(v,d,y,_)}if(Tt(y))return $(v,d,y,_);if(Ot(y)){if(w=Ot(y),typeof w!="function")throw Error(s(150));return y=w.call(y),K(v,d,y,_)}if(typeof y.then=="function")return St(v,d,yu(y),_);if(y.$$typeof===et)return St(v,d,au(v,y),_);gu(v,y)}return typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint"?(y=""+y,d!==null&&d.tag===6?(e(v,d.sibling),_=n(d,y),_.return=v,v=_):(e(v,d),_=ji(y,v.mode,_),_.return=v,v=_),i(v)):e(v,d)}return function(v,d,y,_){try{on=0;var w=St(v,d,y,_);return ya=null,w}catch(Z){if(Z===tn||Z===uu)throw Z;var at=ol(29,Z,null,v.mode);return at.lanes=_,at.return=v,at}finally{}}}var ga=Ho(!0),Co=Ho(!1),Al=D(null),Rl=null;function se(t){var l=t.alternate;j(Yt,Yt.current&1),j(Al,t),Rl===null&&(l===null||da.current!==null||l.memoizedState!==null)&&(Rl=t)}function qo(t){if(t.tag===22){if(j(Yt,Yt.current),j(Al,t),Rl===null){var l=t.alternate;l!==null&&l.memoizedState!==null&&(Rl=t)}}else oe()}function oe(){j(Yt,Yt.current),j(Al,Al.current)}function kl(t){q(Al),Rl===t&&(Rl=null),q(Yt)}var Yt=D(0);function bu(t){for(var l=t;l!==null;){if(l.tag===13){var e=l.memoizedState;if(e!==null&&(e=e.dehydrated,e===null||e.data==="$?"||ef(e)))return l}else if(l.tag===19&&l.memoizedProps.revealOrder!==void 0){if((l.flags&128)!==0)return l}else if(l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break;for(;l.sibling===null;){if(l.return===null||l.return===t)return null;l=l.return}l.sibling.return=l.return,l=l.sibling}return null}function mc(t,l,e,a){l=t.memoizedState,e=e(a,l),e=e==null?l:N({},l,e),t.memoizedState=e,t.lanes===0&&(t.updateQueue.baseState=e)}var hc={enqueueSetState:function(t,l,e){t=t._reactInternals;var a=hl(),n=ie(a);n.payload=l,e!=null&&(n.callback=e),l=ce(t,n,a),l!==null&&(vl(l,t,a),en(l,t,a))},enqueueReplaceState:function(t,l,e){t=t._reactInternals;var a=hl(),n=ie(a);n.tag=1,n.payload=l,e!=null&&(n.callback=e),l=ce(t,n,a),l!==null&&(vl(l,t,a),en(l,t,a))},enqueueForceUpdate:function(t,l){t=t._reactInternals;var e=hl(),a=ie(e);a.tag=2,l!=null&&(a.callback=l),l=ce(t,a,e),l!==null&&(vl(l,t,e),en(l,t,e))}};function Bo(t,l,e,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):l.prototype&&l.prototype.isPureReactComponent?!La(e,a)||!La(n,u):!0}function wo(t,l,e,a){t=l.state,typeof l.componentWillReceiveProps=="function"&&l.componentWillReceiveProps(e,a),typeof l.UNSAFE_componentWillReceiveProps=="function"&&l.UNSAFE_componentWillReceiveProps(e,a),l.state!==t&&hc.enqueueReplaceState(l,l.state,null)}function Ge(t,l){var e=l;if("ref"in l){e={};for(var a in l)a!=="ref"&&(e[a]=l[a])}if(t=t.defaultProps){e===l&&(e=N({},e));for(var n in t)e[n]===void 0&&(e[n]=t[n])}return e}var pu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var l=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(l))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Yo(t){pu(t)}function Go(t){console.error(t)}function Xo(t){pu(t)}function Su(t,l){try{var e=t.onUncaughtError;e(l.value,{componentStack:l.stack})}catch(a){setTimeout(function(){throw a})}}function Qo(t,l,e){try{var a=t.onCaughtError;a(e.value,{componentStack:e.stack,errorBoundary:l.tag===1?l.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function vc(t,l,e){return e=ie(e),e.tag=3,e.payload={element:null},e.callback=function(){Su(t,l)},e}function ko(t){return t=ie(t),t.tag=3,t}function Zo(t,l,e,a){var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){Qo(l,e,a)}}var i=e.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){Qo(l,e,a),typeof n!="function"&&(ye===null?ye=new Set([this]):ye.add(this));var c=a.stack;this.componentDidCatch(a.value,{componentStack:c!==null?c:""})})}function Uh(t,l,e,a,n){if(e.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(l=e.alternate,l!==null&&Fa(l,e,n,!0),e=Al.current,e!==null){switch(e.tag){case 13:return Rl===null?Yc():e.alternate===null&&jt===0&&(jt=3),e.flags&=-257,e.flags|=65536,e.lanes=n,a===Zi?e.flags|=16384:(l=e.updateQueue,l===null?e.updateQueue=new Set([a]):l.add(a),Xc(t,a,n)),!1;case 22:return e.flags|=65536,a===Zi?e.flags|=16384:(l=e.updateQueue,l===null?(l={transitions:null,markerInstances:null,retryQueue:new Set([a])},e.updateQueue=l):(e=l.retryQueue,e===null?l.retryQueue=new Set([a]):e.add(a)),Xc(t,a,n)),!1}throw Error(s(435,e.tag))}return Xc(t,a,n),Yc(),!1}if(ht)return l=Al.current,l!==null?((l.flags&65536)===0&&(l.flags|=256),l.flags|=65536,l.lanes=n,a!==Bi&&(t=Error(s(422),{cause:a}),Wa(bl(t,e)))):(a!==Bi&&(l=Error(s(423),{cause:a}),Wa(bl(l,e))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=bl(a,e),n=vc(t.stateNode,a,n),Ki(t,n),jt!==4&&(jt=2)),!1;var u=Error(s(520),{cause:a});if(u=bl(u,e),bn===null?bn=[u]:bn.push(u),jt!==4&&(jt=2),l===null)return!0;a=bl(a,e),e=l;do{switch(e.tag){case 3:return e.flags|=65536,t=n&-n,e.lanes|=t,t=vc(e.stateNode,a,t),Ki(e,t),!1;case 1:if(l=e.type,u=e.stateNode,(e.flags&128)===0&&(typeof l.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(ye===null||!ye.has(u))))return e.flags|=65536,n&=-n,e.lanes|=n,n=ko(n),Zo(n,t,e,a),Ki(e,n),!1}e=e.return}while(e!==null);return!1}var Vo=Error(s(461)),Qt=!1;function Vt(t,l,e,a){l.child=t===null?Co(l,null,e,a):ga(l,t.child,e,a)}function Lo(t,l,e,a,n){e=e.render;var u=l.ref;if("ref"in a){var i={};for(var c in a)c!=="ref"&&(i[c]=a[c])}else i=a;return Be(l),a=Pi(t,l,e,i,u,n),c=Ii(),t!==null&&!Qt?(tc(t,l,n),Zl(t,l,n)):(ht&&c&&Ci(l),l.flags|=1,Vt(t,l,a,n),l.child)}function Ko(t,l,e,a,n){if(t===null){var u=e.type;return typeof u=="function"&&!Ui(u)&&u.defaultProps===void 0&&e.compare===null?(l.tag=15,l.type=u,Jo(t,l,u,a,n)):(t=In(e.type,null,a,l,l.mode,n),t.ref=l.ref,t.return=l,l.child=t)}if(u=t.child,!Ec(t,n)){var i=u.memoizedProps;if(e=e.compare,e=e!==null?e:La,e(i,a)&&t.ref===l.ref)return Zl(t,l,n)}return l.flags|=1,t=Bl(u,a),t.ref=l.ref,t.return=l,l.child=t}function Jo(t,l,e,a,n){if(t!==null){var u=t.memoizedProps;if(La(u,a)&&t.ref===l.ref)if(Qt=!1,l.pendingProps=a=u,Ec(t,n))(t.flags&131072)!==0&&(Qt=!0);else return l.lanes=t.lanes,Zl(t,l,n)}return yc(t,l,e,a,n)}function $o(t,l,e){var a=l.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((l.flags&128)!==0){if(a=u!==null?u.baseLanes|e:e,t!==null){for(n=l.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;l.childLanes=u&~a}else l.childLanes=0,l.child=null;return Wo(t,l,a,e)}if((e&536870912)!==0)l.memoizedState={baseLanes:0,cachePool:null},t!==null&&nu(l,u!==null?u.cachePool:null),u!==null?Ks(l,u):$i(),qo(l);else return l.lanes=l.childLanes=536870912,Wo(t,l,u!==null?u.baseLanes|e:e,e)}else u!==null?(nu(l,u.cachePool),Ks(l,u),oe(),l.memoizedState=null):(t!==null&&nu(l,null),$i(),oe());return Vt(t,l,n,e),l.child}function Wo(t,l,e,a){var n=ki();return n=n===null?null:{parent:wt._currentValue,pool:n},l.memoizedState={baseLanes:e,cachePool:n},t!==null&&nu(l,null),$i(),qo(l),t!==null&&Fa(t,l,a,!0),null}function xu(t,l){var e=l.ref;if(e===null)t!==null&&t.ref!==null&&(l.flags|=4194816);else{if(typeof e!="function"&&typeof e!="object")throw Error(s(284));(t===null||t.ref!==e)&&(l.flags|=4194816)}}function yc(t,l,e,a,n){return Be(l),e=Pi(t,l,e,a,void 0,n),a=Ii(),t!==null&&!Qt?(tc(t,l,n),Zl(t,l,n)):(ht&&a&&Ci(l),l.flags|=1,Vt(t,l,e,n),l.child)}function Fo(t,l,e,a,n,u){return Be(l),l.updateQueue=null,e=$s(l,a,e,n),Js(t),a=Ii(),t!==null&&!Qt?(tc(t,l,u),Zl(t,l,u)):(ht&&a&&Ci(l),l.flags|=1,Vt(t,l,e,u),l.child)}function Po(t,l,e,a,n){if(Be(l),l.stateNode===null){var u=ca,i=e.contextType;typeof i=="object"&&i!==null&&(u=$t(i)),u=new e(a,u),l.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=hc,l.stateNode=u,u._reactInternals=l,u=l.stateNode,u.props=a,u.state=l.memoizedState,u.refs={},Vi(l),i=e.contextType,u.context=typeof i=="object"&&i!==null?$t(i):ca,u.state=l.memoizedState,i=e.getDerivedStateFromProps,typeof i=="function"&&(mc(l,e,i,a),u.state=l.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&hc.enqueueReplaceState(u,u.state,null),nn(l,a,u,n),an(),u.state=l.memoizedState),typeof u.componentDidMount=="function"&&(l.flags|=4194308),a=!0}else if(t===null){u=l.stateNode;var c=l.memoizedProps,o=Ge(e,c);u.props=o;var b=u.context,z=e.contextType;i=ca,typeof z=="object"&&z!==null&&(i=$t(z));var O=e.getDerivedStateFromProps;z=typeof O=="function"||typeof u.getSnapshotBeforeUpdate=="function",c=l.pendingProps!==c,z||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c||b!==i)&&wo(l,u,a,i),ue=!1;var S=l.memoizedState;u.state=S,nn(l,a,u,n),an(),b=l.memoizedState,c||S!==b||ue?(typeof O=="function"&&(mc(l,e,O,a),b=l.memoizedState),(o=ue||Bo(l,e,o,a,S,b,i))?(z||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(l.flags|=4194308)):(typeof u.componentDidMount=="function"&&(l.flags|=4194308),l.memoizedProps=a,l.memoizedState=b),u.props=a,u.state=b,u.context=i,a=o):(typeof u.componentDidMount=="function"&&(l.flags|=4194308),a=!1)}else{u=l.stateNode,Li(t,l),i=l.memoizedProps,z=Ge(e,i),u.props=z,O=l.pendingProps,S=u.context,b=e.contextType,o=ca,typeof b=="object"&&b!==null&&(o=$t(b)),c=e.getDerivedStateFromProps,(b=typeof c=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==O||S!==o)&&wo(l,u,a,o),ue=!1,S=l.memoizedState,u.state=S,nn(l,a,u,n),an();var x=l.memoizedState;i!==O||S!==x||ue||t!==null&&t.dependencies!==null&&eu(t.dependencies)?(typeof c=="function"&&(mc(l,e,c,a),x=l.memoizedState),(z=ue||Bo(l,e,z,a,S,x,o)||t!==null&&t.dependencies!==null&&eu(t.dependencies))?(b||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,x,o),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,x,o)),typeof u.componentDidUpdate=="function"&&(l.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(l.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(l.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(l.flags|=1024),l.memoizedProps=a,l.memoizedState=x),u.props=a,u.state=x,u.context=o,a=z):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(l.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(l.flags|=1024),a=!1)}return u=a,xu(t,l),a=(l.flags&128)!==0,u||a?(u=l.stateNode,e=a&&typeof e.getDerivedStateFromError!="function"?null:u.render(),l.flags|=1,t!==null&&a?(l.child=ga(l,t.child,null,n),l.child=ga(l,null,e,n)):Vt(t,l,e,n),l.memoizedState=u.state,t=l.child):t=Zl(t,l,n),t}function Io(t,l,e,a){return $a(),l.flags|=256,Vt(t,l,e,a),l.child}var gc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function bc(t){return{baseLanes:t,cachePool:Ys()}}function pc(t,l,e){return t=t!==null?t.childLanes&~e:0,l&&(t|=El),t}function tr(t,l,e){var a=l.pendingProps,n=!1,u=(l.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Yt.current&2)!==0),i&&(n=!0,l.flags&=-129),i=(l.flags&32)!==0,l.flags&=-33,t===null){if(ht){if(n?se(l):oe(),ht){var c=Ut,o;if(o=c){t:{for(o=c,c=Nl;o.nodeType!==8;){if(!c){c=null;break t}if(o=Ol(o.nextSibling),o===null){c=null;break t}}c=o}c!==null?(l.memoizedState={dehydrated:c,treeContext:Ue!==null?{id:wl,overflow:Yl}:null,retryLane:536870912,hydrationErrors:null},o=ol(18,null,null,0),o.stateNode=c,o.return=l,l.child=o,Ft=l,Ut=null,o=!0):o=!1}o||Ce(l)}if(c=l.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return ef(c)?l.lanes=32:l.lanes=536870912,null;kl(l)}return c=a.children,a=a.fallback,n?(oe(),n=l.mode,c=Au({mode:"hidden",children:c},n),a=Re(a,n,e,null),c.return=l,a.return=l,c.sibling=a,l.child=c,n=l.child,n.memoizedState=bc(e),n.childLanes=pc(t,i,e),l.memoizedState=gc,a):(se(l),Sc(l,c))}if(o=t.memoizedState,o!==null&&(c=o.dehydrated,c!==null)){if(u)l.flags&256?(se(l),l.flags&=-257,l=xc(t,l,e)):l.memoizedState!==null?(oe(),l.child=t.child,l.flags|=128,l=null):(oe(),n=a.fallback,c=l.mode,a=Au({mode:"visible",children:a.children},c),n=Re(n,c,e,null),n.flags|=2,a.return=l,n.return=l,a.sibling=n,l.child=a,ga(l,t.child,null,e),a=l.child,a.memoizedState=bc(e),a.childLanes=pc(t,i,e),l.memoizedState=gc,l=n);else if(se(l),ef(c)){if(i=c.nextSibling&&c.nextSibling.dataset,i)var b=i.dgst;i=b,a=Error(s(419)),a.stack="",a.digest=i,Wa({value:a,source:null,stack:null}),l=xc(t,l,e)}else if(Qt||Fa(t,l,e,!1),i=(e&t.childLanes)!==0,Qt||i){if(i=zt,i!==null&&(a=e&-e,a=(a&42)!==0?1:ai(a),a=(a&(i.suspendedLanes|e))!==0?0:a,a!==0&&a!==o.retryLane))throw o.retryLane=a,ia(t,a),vl(i,t,a),Vo;c.data==="$?"||Yc(),l=xc(t,l,e)}else c.data==="$?"?(l.flags|=192,l.child=t.child,l=null):(t=o.treeContext,Ut=Ol(c.nextSibling),Ft=l,ht=!0,He=null,Nl=!1,t!==null&&(Sl[xl++]=wl,Sl[xl++]=Yl,Sl[xl++]=Ue,wl=t.id,Yl=t.overflow,Ue=l),l=Sc(l,a.children),l.flags|=4096);return l}return n?(oe(),n=a.fallback,c=l.mode,o=t.child,b=o.sibling,a=Bl(o,{mode:"hidden",children:a.children}),a.subtreeFlags=o.subtreeFlags&65011712,b!==null?n=Bl(b,n):(n=Re(n,c,e,null),n.flags|=2),n.return=l,a.return=l,a.sibling=n,l.child=a,a=n,n=l.child,c=t.child.memoizedState,c===null?c=bc(e):(o=c.cachePool,o!==null?(b=wt._currentValue,o=o.parent!==b?{parent:b,pool:b}:o):o=Ys(),c={baseLanes:c.baseLanes|e,cachePool:o}),n.memoizedState=c,n.childLanes=pc(t,i,e),l.memoizedState=gc,a):(se(l),e=t.child,t=e.sibling,e=Bl(e,{mode:"visible",children:a.children}),e.return=l,e.sibling=null,t!==null&&(i=l.deletions,i===null?(l.deletions=[t],l.flags|=16):i.push(t)),l.child=e,l.memoizedState=null,e)}function Sc(t,l){return l=Au({mode:"visible",children:l},t.mode),l.return=t,t.child=l}function Au(t,l){return t=ol(22,t,null,l),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function xc(t,l,e){return ga(l,t.child,null,e),t=Sc(l,l.pendingProps.children),t.flags|=2,l.memoizedState=null,t}function lr(t,l,e){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l),Yi(t.return,l,e)}function Ac(t,l,e,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:l,rendering:null,renderingStartTime:0,last:a,tail:e,tailMode:n}:(u.isBackwards=l,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=e,u.tailMode=n)}function er(t,l,e){var a=l.pendingProps,n=a.revealOrder,u=a.tail;if(Vt(t,l,a.children,e),a=Yt.current,(a&2)!==0)a=a&1|2,l.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=l.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&lr(t,e,l);else if(t.tag===19)lr(t,e,l);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===l)break t;for(;t.sibling===null;){if(t.return===null||t.return===l)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(j(Yt,a),n){case"forwards":for(e=l.child,n=null;e!==null;)t=e.alternate,t!==null&&bu(t)===null&&(n=e),e=e.sibling;e=n,e===null?(n=l.child,l.child=null):(n=e.sibling,e.sibling=null),Ac(l,!1,n,e,u);break;case"backwards":for(e=null,n=l.child,l.child=null;n!==null;){if(t=n.alternate,t!==null&&bu(t)===null){l.child=n;break}t=n.sibling,n.sibling=e,e=n,n=t}Ac(l,!0,e,null,u);break;case"together":Ac(l,!1,null,null,void 0);break;default:l.memoizedState=null}return l.child}function Zl(t,l,e){if(t!==null&&(l.dependencies=t.dependencies),ve|=l.lanes,(e&l.childLanes)===0)if(t!==null){if(Fa(t,l,e,!1),(e&l.childLanes)===0)return null}else return null;if(t!==null&&l.child!==t.child)throw Error(s(153));if(l.child!==null){for(t=l.child,e=Bl(t,t.pendingProps),l.child=e,e.return=l;t.sibling!==null;)t=t.sibling,e=e.sibling=Bl(t,t.pendingProps),e.return=l;e.sibling=null}return l.child}function Ec(t,l){return(t.lanes&l)!==0?!0:(t=t.dependencies,!!(t!==null&&eu(t)))}function jh(t,l,e){switch(l.tag){case 3:xt(l,l.stateNode.containerInfo),ne(l,wt,t.memoizedState.cache),$a();break;case 27:case 5:Pl(l);break;case 4:xt(l,l.stateNode.containerInfo);break;case 10:ne(l,l.type,l.memoizedProps.value);break;case 13:var a=l.memoizedState;if(a!==null)return a.dehydrated!==null?(se(l),l.flags|=128,null):(e&l.child.childLanes)!==0?tr(t,l,e):(se(l),t=Zl(t,l,e),t!==null?t.sibling:null);se(l);break;case 19:var n=(t.flags&128)!==0;if(a=(e&l.childLanes)!==0,a||(Fa(t,l,e,!1),a=(e&l.childLanes)!==0),n){if(a)return er(t,l,e);l.flags|=128}if(n=l.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),j(Yt,Yt.current),a)break;return null;case 22:case 23:return l.lanes=0,$o(t,l,e);case 24:ne(l,wt,t.memoizedState.cache)}return Zl(t,l,e)}function ar(t,l,e){if(t!==null)if(t.memoizedProps!==l.pendingProps)Qt=!0;else{if(!Ec(t,e)&&(l.flags&128)===0)return Qt=!1,jh(t,l,e);Qt=(t.flags&131072)!==0}else Qt=!1,ht&&(l.flags&1048576)!==0&&Us(l,lu,l.index);switch(l.lanes=0,l.tag){case 16:t:{t=l.pendingProps;var a=l.elementType,n=a._init;if(a=n(a._payload),l.type=a,typeof a=="function")Ui(a)?(t=Ge(a,t),l.tag=1,l=Po(null,l,a,t,e)):(l.tag=0,l=yc(null,l,a,t,e));else{if(a!=null){if(n=a.$$typeof,n===_t){l.tag=11,l=Lo(null,l,a,t,e);break t}else if(n===Rt){l.tag=14,l=Ko(null,l,a,t,e);break t}}throw l=It(a)||a,Error(s(306,l,""))}}return l;case 0:return yc(t,l,l.type,l.pendingProps,e);case 1:return a=l.type,n=Ge(a,l.pendingProps),Po(t,l,a,n,e);case 3:t:{if(xt(l,l.stateNode.containerInfo),t===null)throw Error(s(387));a=l.pendingProps;var u=l.memoizedState;n=u.element,Li(t,l),nn(l,a,null,e);var i=l.memoizedState;if(a=i.cache,ne(l,wt,a),a!==u.cache&&Gi(l,[wt],e,!0),an(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},l.updateQueue.baseState=u,l.memoizedState=u,l.flags&256){l=Io(t,l,a,e);break t}else if(a!==n){n=bl(Error(s(424)),l),Wa(n),l=Io(t,l,a,e);break t}else{switch(t=l.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ut=Ol(t.firstChild),Ft=l,ht=!0,He=null,Nl=!0,e=Co(l,null,a,e),l.child=e;e;)e.flags=e.flags&-3|4096,e=e.sibling}else{if($a(),a===n){l=Zl(t,l,e);break t}Vt(t,l,a,e)}l=l.child}return l;case 26:return xu(t,l),t===null?(e=cd(l.type,null,l.pendingProps,null))?l.memoizedState=e:ht||(e=l.type,t=l.pendingProps,a=qu(W.current).createElement(e),a[Jt]=l,a[tl]=t,Kt(a,e,t),Xt(a),l.stateNode=a):l.memoizedState=cd(l.type,t.memoizedProps,l.pendingProps,t.memoizedState),null;case 27:return Pl(l),t===null&&ht&&(a=l.stateNode=nd(l.type,l.pendingProps,W.current),Ft=l,Nl=!0,n=Ut,pe(l.type)?(af=n,Ut=Ol(a.firstChild)):Ut=n),Vt(t,l,l.pendingProps.children,e),xu(t,l),t===null&&(l.flags|=4194304),l.child;case 5:return t===null&&ht&&((n=a=Ut)&&(a=c0(a,l.type,l.pendingProps,Nl),a!==null?(l.stateNode=a,Ft=l,Ut=Ol(a.firstChild),Nl=!1,n=!0):n=!1),n||Ce(l)),Pl(l),n=l.type,u=l.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,Ic(n,u)?a=null:i!==null&&Ic(n,i)&&(l.flags|=32),l.memoizedState!==null&&(n=Pi(t,l,zh,null,null,e),_n._currentValue=n),xu(t,l),Vt(t,l,a,e),l.child;case 6:return t===null&&ht&&((t=e=Ut)&&(e=f0(e,l.pendingProps,Nl),e!==null?(l.stateNode=e,Ft=l,Ut=null,t=!0):t=!1),t||Ce(l)),null;case 13:return tr(t,l,e);case 4:return xt(l,l.stateNode.containerInfo),a=l.pendingProps,t===null?l.child=ga(l,null,a,e):Vt(t,l,a,e),l.child;case 11:return Lo(t,l,l.type,l.pendingProps,e);case 7:return Vt(t,l,l.pendingProps,e),l.child;case 8:return Vt(t,l,l.pendingProps.children,e),l.child;case 12:return Vt(t,l,l.pendingProps.children,e),l.child;case 10:return a=l.pendingProps,ne(l,l.type,a.value),Vt(t,l,a.children,e),l.child;case 9:return n=l.type._context,a=l.pendingProps.children,Be(l),n=$t(n),a=a(n),l.flags|=1,Vt(t,l,a,e),l.child;case 14:return Ko(t,l,l.type,l.pendingProps,e);case 15:return Jo(t,l,l.type,l.pendingProps,e);case 19:return er(t,l,e);case 31:return a=l.pendingProps,e=l.mode,a={mode:a.mode,children:a.children},t===null?(e=Au(a,e),e.ref=l.ref,l.child=e,e.return=l,l=e):(e=Bl(t.child,a),e.ref=l.ref,l.child=e,e.return=l,l=e),l;case 22:return $o(t,l,e);case 24:return Be(l),a=$t(wt),t===null?(n=ki(),n===null&&(n=zt,u=Xi(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=e),n=u),l.memoizedState={parent:a,cache:n},Vi(l),ne(l,wt,n)):((t.lanes&e)!==0&&(Li(t,l),nn(l,null,null,e),an()),n=t.memoizedState,u=l.memoizedState,n.parent!==a?(n={parent:a,cache:a},l.memoizedState=n,l.lanes===0&&(l.memoizedState=l.updateQueue.baseState=n),ne(l,wt,a)):(a=u.cache,ne(l,wt,a),a!==n.cache&&Gi(l,[wt],e,!0))),Vt(t,l,l.pendingProps.children,e),l.child;case 29:throw l.pendingProps}throw Error(s(156,l.tag))}function Vl(t){t.flags|=4}function nr(t,l){if(l.type!=="stylesheet"||(l.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!dd(l)){if(l=Al.current,l!==null&&((st&4194048)===st?Rl!==null:(st&62914560)!==st&&(st&536870912)===0||l!==Rl))throw ln=Zi,Gs;t.flags|=8192}}function Eu(t,l){l!==null&&(t.flags|=4),t.flags&16384&&(l=t.tag!==22?Cf():536870912,t.lanes|=l,xa|=l)}function dn(t,l){if(!ht)switch(t.tailMode){case"hidden":l=t.tail;for(var e=null;l!==null;)l.alternate!==null&&(e=l),l=l.sibling;e===null?t.tail=null:e.sibling=null;break;case"collapsed":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?l||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Nt(t){var l=t.alternate!==null&&t.alternate.child===t.child,e=0,a=0;if(l)for(var n=t.child;n!==null;)e|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)e|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=e,l}function Hh(t,l,e){var a=l.pendingProps;switch(qi(l),l.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Nt(l),null;case 1:return Nt(l),null;case 3:return e=l.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),l.memoizedState.cache!==a&&(l.flags|=2048),Xl(wt),il(),e.pendingContext&&(e.context=e.pendingContext,e.pendingContext=null),(t===null||t.child===null)&&(Ja(l)?Vl(l):t===null||t.memoizedState.isDehydrated&&(l.flags&256)===0||(l.flags|=1024,Cs())),Nt(l),null;case 26:return e=l.memoizedState,t===null?(Vl(l),e!==null?(Nt(l),nr(l,e)):(Nt(l),l.flags&=-16777217)):e?e!==t.memoizedState?(Vl(l),Nt(l),nr(l,e)):(Nt(l),l.flags&=-16777217):(t.memoizedProps!==a&&Vl(l),Nt(l),l.flags&=-16777217),null;case 27:Il(l),e=W.current;var n=l.type;if(t!==null&&l.stateNode!=null)t.memoizedProps!==a&&Vl(l);else{if(!a){if(l.stateNode===null)throw Error(s(166));return Nt(l),null}t=B.current,Ja(l)?js(l):(t=nd(n,a,e),l.stateNode=t,Vl(l))}return Nt(l),null;case 5:if(Il(l),e=l.type,t!==null&&l.stateNode!=null)t.memoizedProps!==a&&Vl(l);else{if(!a){if(l.stateNode===null)throw Error(s(166));return Nt(l),null}if(t=B.current,Ja(l))js(l);else{switch(n=qu(W.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",e);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",e);break;default:switch(e){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",e);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",e);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(e,{is:a.is}):n.createElement(e)}}t[Jt]=l,t[tl]=a;t:for(n=l.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===l)break t;for(;n.sibling===null;){if(n.return===null||n.return===l)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}l.stateNode=t;t:switch(Kt(t,e,a),e){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Vl(l)}}return Nt(l),l.flags&=-16777217,null;case 6:if(t&&l.stateNode!=null)t.memoizedProps!==a&&Vl(l);else{if(typeof a!="string"&&l.stateNode===null)throw Error(s(166));if(t=W.current,Ja(l)){if(t=l.stateNode,e=l.memoizedProps,a=null,n=Ft,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Jt]=l,t=!!(t.nodeValue===e||a!==null&&a.suppressHydrationWarning===!0||Fr(t.nodeValue,e)),t||Ce(l)}else t=qu(t).createTextNode(a),t[Jt]=l,l.stateNode=t}return Nt(l),null;case 13:if(a=l.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Ja(l),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(s(318));if(n=l.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[Jt]=l}else $a(),(l.flags&128)===0&&(l.memoizedState=null),l.flags|=4;Nt(l),n=!1}else n=Cs(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return l.flags&256?(kl(l),l):(kl(l),null)}if(kl(l),(l.flags&128)!==0)return l.lanes=e,l;if(e=a!==null,t=t!==null&&t.memoizedState!==null,e){a=l.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return e!==t&&e&&(l.child.flags|=8192),Eu(l,l.updateQueue),Nt(l),null;case 4:return il(),t===null&&Jc(l.stateNode.containerInfo),Nt(l),null;case 10:return Xl(l.type),Nt(l),null;case 19:if(q(Yt),n=l.memoizedState,n===null)return Nt(l),null;if(a=(l.flags&128)!==0,u=n.rendering,u===null)if(a)dn(n,!1);else{if(jt!==0||t!==null&&(t.flags&128)!==0)for(t=l.child;t!==null;){if(u=bu(t),u!==null){for(l.flags|=128,dn(n,!1),t=u.updateQueue,l.updateQueue=t,Eu(l,t),l.subtreeFlags=0,t=e,e=l.child;e!==null;)Rs(e,t),e=e.sibling;return j(Yt,Yt.current&1|2),l.child}t=t.sibling}n.tail!==null&&Dl()>Mu&&(l.flags|=128,a=!0,dn(n,!1),l.lanes=4194304)}else{if(!a)if(t=bu(u),t!==null){if(l.flags|=128,a=!0,t=t.updateQueue,l.updateQueue=t,Eu(l,t),dn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!ht)return Nt(l),null}else 2*Dl()-n.renderingStartTime>Mu&&e!==536870912&&(l.flags|=128,a=!0,dn(n,!1),l.lanes=4194304);n.isBackwards?(u.sibling=l.child,l.child=u):(t=n.last,t!==null?t.sibling=u:l.child=u,n.last=u)}return n.tail!==null?(l=n.tail,n.rendering=l,n.tail=l.sibling,n.renderingStartTime=Dl(),l.sibling=null,t=Yt.current,j(Yt,a?t&1|2:t&1),l):(Nt(l),null);case 22:case 23:return kl(l),Wi(),a=l.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(l.flags|=8192):a&&(l.flags|=8192),a?(e&536870912)!==0&&(l.flags&128)===0&&(Nt(l),l.subtreeFlags&6&&(l.flags|=8192)):Nt(l),e=l.updateQueue,e!==null&&Eu(l,e.retryQueue),e=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),a=null,l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(a=l.memoizedState.cachePool.pool),a!==e&&(l.flags|=2048),t!==null&&q(we),null;case 24:return e=null,t!==null&&(e=t.memoizedState.cache),l.memoizedState.cache!==e&&(l.flags|=2048),Xl(wt),Nt(l),null;case 25:return null;case 30:return null}throw Error(s(156,l.tag))}function Ch(t,l){switch(qi(l),l.tag){case 1:return t=l.flags,t&65536?(l.flags=t&-65537|128,l):null;case 3:return Xl(wt),il(),t=l.flags,(t&65536)!==0&&(t&128)===0?(l.flags=t&-65537|128,l):null;case 26:case 27:case 5:return Il(l),null;case 13:if(kl(l),t=l.memoizedState,t!==null&&t.dehydrated!==null){if(l.alternate===null)throw Error(s(340));$a()}return t=l.flags,t&65536?(l.flags=t&-65537|128,l):null;case 19:return q(Yt),null;case 4:return il(),null;case 10:return Xl(l.type),null;case 22:case 23:return kl(l),Wi(),t!==null&&q(we),t=l.flags,t&65536?(l.flags=t&-65537|128,l):null;case 24:return Xl(wt),null;case 25:return null;default:return null}}function ur(t,l){switch(qi(l),l.tag){case 3:Xl(wt),il();break;case 26:case 27:case 5:Il(l);break;case 4:il();break;case 13:kl(l);break;case 19:q(Yt);break;case 10:Xl(l.type);break;case 22:case 23:kl(l),Wi(),t!==null&&q(we);break;case 24:Xl(wt)}}function mn(t,l){try{var e=l.updateQueue,a=e!==null?e.lastEffect:null;if(a!==null){var n=a.next;e=n;do{if((e.tag&t)===t){a=void 0;var u=e.create,i=e.inst;a=u(),i.destroy=a}e=e.next}while(e!==n)}}catch(c){At(l,l.return,c)}}function re(t,l,e){try{var a=l.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,c=i.destroy;if(c!==void 0){i.destroy=void 0,n=l;var o=e,b=c;try{b()}catch(z){At(n,o,z)}}}a=a.next}while(a!==u)}}catch(z){At(l,l.return,z)}}function ir(t){var l=t.updateQueue;if(l!==null){var e=t.stateNode;try{Ls(l,e)}catch(a){At(t,t.return,a)}}}function cr(t,l,e){e.props=Ge(t.type,t.memoizedProps),e.state=t.memoizedState;try{e.componentWillUnmount()}catch(a){At(t,l,a)}}function hn(t,l){try{var e=t.ref;if(e!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof e=="function"?t.refCleanup=e(a):e.current=a}}catch(n){At(t,l,n)}}function Ul(t,l){var e=t.ref,a=t.refCleanup;if(e!==null)if(typeof a=="function")try{a()}catch(n){At(t,l,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof e=="function")try{e(null)}catch(n){At(t,l,n)}else e.current=null}function fr(t){var l=t.type,e=t.memoizedProps,a=t.stateNode;try{t:switch(l){case"button":case"input":case"select":case"textarea":e.autoFocus&&a.focus();break t;case"img":e.src?a.src=e.src:e.srcSet&&(a.srcset=e.srcSet)}}catch(n){At(t,t.return,n)}}function Tc(t,l,e){try{var a=t.stateNode;e0(a,t.type,e,l),a[tl]=l}catch(n){At(t,t.return,n)}}function sr(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&pe(t.type)||t.tag===4}function zc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||sr(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&pe(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Mc(t,l,e){var a=t.tag;if(a===5||a===6)t=t.stateNode,l?(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e).insertBefore(t,l):(l=e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.appendChild(t),e=e._reactRootContainer,e!=null||l.onclick!==null||(l.onclick=Cu));else if(a!==4&&(a===27&&pe(t.type)&&(e=t.stateNode,l=null),t=t.child,t!==null))for(Mc(t,l,e),t=t.sibling;t!==null;)Mc(t,l,e),t=t.sibling}function Tu(t,l,e){var a=t.tag;if(a===5||a===6)t=t.stateNode,l?e.insertBefore(t,l):e.appendChild(t);else if(a!==4&&(a===27&&pe(t.type)&&(e=t.stateNode),t=t.child,t!==null))for(Tu(t,l,e),t=t.sibling;t!==null;)Tu(t,l,e),t=t.sibling}function or(t){var l=t.stateNode,e=t.memoizedProps;try{for(var a=t.type,n=l.attributes;n.length;)l.removeAttributeNode(n[0]);Kt(l,a,e),l[Jt]=t,l[tl]=e}catch(u){At(t,t.return,u)}}var Ll=!1,Ct=!1,_c=!1,rr=typeof WeakSet=="function"?WeakSet:Set,kt=null;function qh(t,l){if(t=t.containerInfo,Fc=Qu,t=xs(t),zi(t)){if("selectionStart"in t)var e={start:t.selectionStart,end:t.selectionEnd};else t:{e=(e=t.ownerDocument)&&e.defaultView||window;var a=e.getSelection&&e.getSelection();if(a&&a.rangeCount!==0){e=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{e.nodeType,u.nodeType}catch{e=null;break t}var i=0,c=-1,o=-1,b=0,z=0,O=t,S=null;l:for(;;){for(var x;O!==e||n!==0&&O.nodeType!==3||(c=i+n),O!==u||a!==0&&O.nodeType!==3||(o=i+a),O.nodeType===3&&(i+=O.nodeValue.length),(x=O.firstChild)!==null;)S=O,O=x;for(;;){if(O===t)break l;if(S===e&&++b===n&&(c=i),S===u&&++z===a&&(o=i),(x=O.nextSibling)!==null)break;O=S,S=O.parentNode}O=x}e=c===-1||o===-1?null:{start:c,end:o}}else e=null}e=e||{start:0,end:0}}else e=null;for(Pc={focusedElem:t,selectionRange:e},Qu=!1,kt=l;kt!==null;)if(l=kt,t=l.child,(l.subtreeFlags&1024)!==0&&t!==null)t.return=l,kt=t;else for(;kt!==null;){switch(l=kt,u=l.alternate,t=l.flags,l.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,e=l,n=u.memoizedProps,u=u.memoizedState,a=e.stateNode;try{var $=Ge(e.type,n,e.elementType===e.type);t=a.getSnapshotBeforeUpdate($,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(K){At(e,e.return,K)}}break;case 3:if((t&1024)!==0){if(t=l.stateNode.containerInfo,e=t.nodeType,e===9)lf(t);else if(e===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":lf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=l.sibling,t!==null){t.return=l.return,kt=t;break}kt=l.return}}function dr(t,l,e){var a=e.flags;switch(e.tag){case 0:case 11:case 15:de(t,e),a&4&&mn(5,e);break;case 1:if(de(t,e),a&4)if(t=e.stateNode,l===null)try{t.componentDidMount()}catch(i){At(e,e.return,i)}else{var n=Ge(e.type,l.memoizedProps);l=l.memoizedState;try{t.componentDidUpdate(n,l,t.__reactInternalSnapshotBeforeUpdate)}catch(i){At(e,e.return,i)}}a&64&&ir(e),a&512&&hn(e,e.return);break;case 3:if(de(t,e),a&64&&(t=e.updateQueue,t!==null)){if(l=null,e.child!==null)switch(e.child.tag){case 27:case 5:l=e.child.stateNode;break;case 1:l=e.child.stateNode}try{Ls(t,l)}catch(i){At(e,e.return,i)}}break;case 27:l===null&&a&4&&or(e);case 26:case 5:de(t,e),l===null&&a&4&&fr(e),a&512&&hn(e,e.return);break;case 12:de(t,e);break;case 13:de(t,e),a&4&&vr(t,e),a&64&&(t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(e=Vh.bind(null,e),s0(t,e))));break;case 22:if(a=e.memoizedState!==null||Ll,!a){l=l!==null&&l.memoizedState!==null||Ct,n=Ll;var u=Ct;Ll=a,(Ct=l)&&!u?me(t,e,(e.subtreeFlags&8772)!==0):de(t,e),Ll=n,Ct=u}break;case 30:break;default:de(t,e)}}function mr(t){var l=t.alternate;l!==null&&(t.alternate=null,mr(l)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(l=t.stateNode,l!==null&&ii(l)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Dt=null,al=!1;function Kl(t,l,e){for(e=e.child;e!==null;)hr(t,l,e),e=e.sibling}function hr(t,l,e){if(cl&&typeof cl.onCommitFiberUnmount=="function")try{cl.onCommitFiberUnmount(Ha,e)}catch{}switch(e.tag){case 26:Ct||Ul(e,l),Kl(t,l,e),e.memoizedState?e.memoizedState.count--:e.stateNode&&(e=e.stateNode,e.parentNode.removeChild(e));break;case 27:Ct||Ul(e,l);var a=Dt,n=al;pe(e.type)&&(Dt=e.stateNode,al=!1),Kl(t,l,e),En(e.stateNode),Dt=a,al=n;break;case 5:Ct||Ul(e,l);case 6:if(a=Dt,n=al,Dt=null,Kl(t,l,e),Dt=a,al=n,Dt!==null)if(al)try{(Dt.nodeType===9?Dt.body:Dt.nodeName==="HTML"?Dt.ownerDocument.body:Dt).removeChild(e.stateNode)}catch(u){At(e,l,u)}else try{Dt.removeChild(e.stateNode)}catch(u){At(e,l,u)}break;case 18:Dt!==null&&(al?(t=Dt,ed(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,e.stateNode),Rn(t)):ed(Dt,e.stateNode));break;case 4:a=Dt,n=al,Dt=e.stateNode.containerInfo,al=!0,Kl(t,l,e),Dt=a,al=n;break;case 0:case 11:case 14:case 15:Ct||re(2,e,l),Ct||re(4,e,l),Kl(t,l,e);break;case 1:Ct||(Ul(e,l),a=e.stateNode,typeof a.componentWillUnmount=="function"&&cr(e,l,a)),Kl(t,l,e);break;case 21:Kl(t,l,e);break;case 22:Ct=(a=Ct)||e.memoizedState!==null,Kl(t,l,e),Ct=a;break;default:Kl(t,l,e)}}function vr(t,l){if(l.memoizedState===null&&(t=l.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Rn(t)}catch(e){At(l,l.return,e)}}function Bh(t){switch(t.tag){case 13:case 19:var l=t.stateNode;return l===null&&(l=t.stateNode=new rr),l;case 22:return t=t.stateNode,l=t._retryCache,l===null&&(l=t._retryCache=new rr),l;default:throw Error(s(435,t.tag))}}function Oc(t,l){var e=Bh(t);l.forEach(function(a){var n=Lh.bind(null,t,a);e.has(a)||(e.add(a),a.then(n,n))})}function rl(t,l){var e=l.deletions;if(e!==null)for(var a=0;a<e.length;a++){var n=e[a],u=t,i=l,c=i;t:for(;c!==null;){switch(c.tag){case 27:if(pe(c.type)){Dt=c.stateNode,al=!1;break t}break;case 5:Dt=c.stateNode,al=!1;break t;case 3:case 4:Dt=c.stateNode.containerInfo,al=!0;break t}c=c.return}if(Dt===null)throw Error(s(160));hr(u,i,n),Dt=null,al=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(l.subtreeFlags&13878)for(l=l.child;l!==null;)yr(l,t),l=l.sibling}var _l=null;function yr(t,l){var e=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:rl(l,t),dl(t),a&4&&(re(3,t,t.return),mn(3,t),re(5,t,t.return));break;case 1:rl(l,t),dl(t),a&512&&(Ct||e===null||Ul(e,e.return)),a&64&&Ll&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(e=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=e===null?a:e.concat(a))));break;case 26:var n=_l;if(rl(l,t),dl(t),a&512&&(Ct||e===null||Ul(e,e.return)),a&4){var u=e!==null?e.memoizedState:null;if(a=t.memoizedState,e===null)if(a===null)if(t.stateNode===null){t:{a=t.type,e=t.memoizedProps,n=n.ownerDocument||n;l:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ba]||u[Jt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Kt(u,a,e),u[Jt]=t,Xt(u),a=u;break t;case"link":var i=od("link","href",n).get(a+(e.href||""));if(i){for(var c=0;c<i.length;c++)if(u=i[c],u.getAttribute("href")===(e.href==null||e.href===""?null:e.href)&&u.getAttribute("rel")===(e.rel==null?null:e.rel)&&u.getAttribute("title")===(e.title==null?null:e.title)&&u.getAttribute("crossorigin")===(e.crossOrigin==null?null:e.crossOrigin)){i.splice(c,1);break l}}u=n.createElement(a),Kt(u,a,e),n.head.appendChild(u);break;case"meta":if(i=od("meta","content",n).get(a+(e.content||""))){for(c=0;c<i.length;c++)if(u=i[c],u.getAttribute("content")===(e.content==null?null:""+e.content)&&u.getAttribute("name")===(e.name==null?null:e.name)&&u.getAttribute("property")===(e.property==null?null:e.property)&&u.getAttribute("http-equiv")===(e.httpEquiv==null?null:e.httpEquiv)&&u.getAttribute("charset")===(e.charSet==null?null:e.charSet)){i.splice(c,1);break l}}u=n.createElement(a),Kt(u,a,e),n.head.appendChild(u);break;default:throw Error(s(468,a))}u[Jt]=t,Xt(u),a=u}t.stateNode=a}else rd(n,t.type,t.stateNode);else t.stateNode=sd(n,a,t.memoizedProps);else u!==a?(u===null?e.stateNode!==null&&(e=e.stateNode,e.parentNode.removeChild(e)):u.count--,a===null?rd(n,t.type,t.stateNode):sd(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Tc(t,t.memoizedProps,e.memoizedProps)}break;case 27:rl(l,t),dl(t),a&512&&(Ct||e===null||Ul(e,e.return)),e!==null&&a&4&&Tc(t,t.memoizedProps,e.memoizedProps);break;case 5:if(rl(l,t),dl(t),a&512&&(Ct||e===null||Ul(e,e.return)),t.flags&32){n=t.stateNode;try{Ie(n,"")}catch(x){At(t,t.return,x)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Tc(t,n,e!==null?e.memoizedProps:n)),a&1024&&(_c=!0);break;case 6:if(rl(l,t),dl(t),a&4){if(t.stateNode===null)throw Error(s(162));a=t.memoizedProps,e=t.stateNode;try{e.nodeValue=a}catch(x){At(t,t.return,x)}}break;case 3:if(Yu=null,n=_l,_l=Bu(l.containerInfo),rl(l,t),_l=n,dl(t),a&4&&e!==null&&e.memoizedState.isDehydrated)try{Rn(l.containerInfo)}catch(x){At(t,t.return,x)}_c&&(_c=!1,gr(t));break;case 4:a=_l,_l=Bu(t.stateNode.containerInfo),rl(l,t),dl(t),_l=a;break;case 12:rl(l,t),dl(t);break;case 13:rl(l,t),dl(t),t.child.flags&8192&&t.memoizedState!==null!=(e!==null&&e.memoizedState!==null)&&(Hc=Dl()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Oc(t,a)));break;case 22:n=t.memoizedState!==null;var o=e!==null&&e.memoizedState!==null,b=Ll,z=Ct;if(Ll=b||n,Ct=z||o,rl(l,t),Ct=z,Ll=b,dl(t),a&8192)t:for(l=t.stateNode,l._visibility=n?l._visibility&-2:l._visibility|1,n&&(e===null||o||Ll||Ct||Xe(t)),e=null,l=t;;){if(l.tag===5||l.tag===26){if(e===null){o=e=l;try{if(u=o.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{c=o.stateNode;var O=o.memoizedProps.style,S=O!=null&&O.hasOwnProperty("display")?O.display:null;c.style.display=S==null||typeof S=="boolean"?"":(""+S).trim()}}catch(x){At(o,o.return,x)}}}else if(l.tag===6){if(e===null){o=l;try{o.stateNode.nodeValue=n?"":o.memoizedProps}catch(x){At(o,o.return,x)}}}else if((l.tag!==22&&l.tag!==23||l.memoizedState===null||l===t)&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break t;for(;l.sibling===null;){if(l.return===null||l.return===t)break t;e===l&&(e=null),l=l.return}e===l&&(e=null),l.sibling.return=l.return,l=l.sibling}a&4&&(a=t.updateQueue,a!==null&&(e=a.retryQueue,e!==null&&(a.retryQueue=null,Oc(t,e))));break;case 19:rl(l,t),dl(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Oc(t,a)));break;case 30:break;case 21:break;default:rl(l,t),dl(t)}}function dl(t){var l=t.flags;if(l&2){try{for(var e,a=t.return;a!==null;){if(sr(a)){e=a;break}a=a.return}if(e==null)throw Error(s(160));switch(e.tag){case 27:var n=e.stateNode,u=zc(t);Tu(t,u,n);break;case 5:var i=e.stateNode;e.flags&32&&(Ie(i,""),e.flags&=-33);var c=zc(t);Tu(t,c,i);break;case 3:case 4:var o=e.stateNode.containerInfo,b=zc(t);Mc(t,b,o);break;default:throw Error(s(161))}}catch(z){At(t,t.return,z)}t.flags&=-3}l&4096&&(t.flags&=-4097)}function gr(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var l=t;gr(l),l.tag===5&&l.flags&1024&&l.stateNode.reset(),t=t.sibling}}function de(t,l){if(l.subtreeFlags&8772)for(l=l.child;l!==null;)dr(t,l.alternate,l),l=l.sibling}function Xe(t){for(t=t.child;t!==null;){var l=t;switch(l.tag){case 0:case 11:case 14:case 15:re(4,l,l.return),Xe(l);break;case 1:Ul(l,l.return);var e=l.stateNode;typeof e.componentWillUnmount=="function"&&cr(l,l.return,e),Xe(l);break;case 27:En(l.stateNode);case 26:case 5:Ul(l,l.return),Xe(l);break;case 22:l.memoizedState===null&&Xe(l);break;case 30:Xe(l);break;default:Xe(l)}t=t.sibling}}function me(t,l,e){for(e=e&&(l.subtreeFlags&8772)!==0,l=l.child;l!==null;){var a=l.alternate,n=t,u=l,i=u.flags;switch(u.tag){case 0:case 11:case 15:me(n,u,e),mn(4,u);break;case 1:if(me(n,u,e),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(b){At(a,a.return,b)}if(a=u,n=a.updateQueue,n!==null){var c=a.stateNode;try{var o=n.shared.hiddenCallbacks;if(o!==null)for(n.shared.hiddenCallbacks=null,n=0;n<o.length;n++)Vs(o[n],c)}catch(b){At(a,a.return,b)}}e&&i&64&&ir(u),hn(u,u.return);break;case 27:or(u);case 26:case 5:me(n,u,e),e&&a===null&&i&4&&fr(u),hn(u,u.return);break;case 12:me(n,u,e);break;case 13:me(n,u,e),e&&i&4&&vr(n,u);break;case 22:u.memoizedState===null&&me(n,u,e),hn(u,u.return);break;case 30:break;default:me(n,u,e)}l=l.sibling}}function Dc(t,l){var e=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),t=null,l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(t=l.memoizedState.cachePool.pool),t!==e&&(t!=null&&t.refCount++,e!=null&&Pa(e))}function Nc(t,l){t=null,l.alternate!==null&&(t=l.alternate.memoizedState.cache),l=l.memoizedState.cache,l!==t&&(l.refCount++,t!=null&&Pa(t))}function jl(t,l,e,a){if(l.subtreeFlags&10256)for(l=l.child;l!==null;)br(t,l,e,a),l=l.sibling}function br(t,l,e,a){var n=l.flags;switch(l.tag){case 0:case 11:case 15:jl(t,l,e,a),n&2048&&mn(9,l);break;case 1:jl(t,l,e,a);break;case 3:jl(t,l,e,a),n&2048&&(t=null,l.alternate!==null&&(t=l.alternate.memoizedState.cache),l=l.memoizedState.cache,l!==t&&(l.refCount++,t!=null&&Pa(t)));break;case 12:if(n&2048){jl(t,l,e,a),t=l.stateNode;try{var u=l.memoizedProps,i=u.id,c=u.onPostCommit;typeof c=="function"&&c(i,l.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(o){At(l,l.return,o)}}else jl(t,l,e,a);break;case 13:jl(t,l,e,a);break;case 23:break;case 22:u=l.stateNode,i=l.alternate,l.memoizedState!==null?u._visibility&2?jl(t,l,e,a):vn(t,l):u._visibility&2?jl(t,l,e,a):(u._visibility|=2,ba(t,l,e,a,(l.subtreeFlags&10256)!==0)),n&2048&&Dc(i,l);break;case 24:jl(t,l,e,a),n&2048&&Nc(l.alternate,l);break;default:jl(t,l,e,a)}}function ba(t,l,e,a,n){for(n=n&&(l.subtreeFlags&10256)!==0,l=l.child;l!==null;){var u=t,i=l,c=e,o=a,b=i.flags;switch(i.tag){case 0:case 11:case 15:ba(u,i,c,o,n),mn(8,i);break;case 23:break;case 22:var z=i.stateNode;i.memoizedState!==null?z._visibility&2?ba(u,i,c,o,n):vn(u,i):(z._visibility|=2,ba(u,i,c,o,n)),n&&b&2048&&Dc(i.alternate,i);break;case 24:ba(u,i,c,o,n),n&&b&2048&&Nc(i.alternate,i);break;default:ba(u,i,c,o,n)}l=l.sibling}}function vn(t,l){if(l.subtreeFlags&10256)for(l=l.child;l!==null;){var e=t,a=l,n=a.flags;switch(a.tag){case 22:vn(e,a),n&2048&&Dc(a.alternate,a);break;case 24:vn(e,a),n&2048&&Nc(a.alternate,a);break;default:vn(e,a)}l=l.sibling}}var yn=8192;function pa(t){if(t.subtreeFlags&yn)for(t=t.child;t!==null;)pr(t),t=t.sibling}function pr(t){switch(t.tag){case 26:pa(t),t.flags&yn&&t.memoizedState!==null&&A0(_l,t.memoizedState,t.memoizedProps);break;case 5:pa(t);break;case 3:case 4:var l=_l;_l=Bu(t.stateNode.containerInfo),pa(t),_l=l;break;case 22:t.memoizedState===null&&(l=t.alternate,l!==null&&l.memoizedState!==null?(l=yn,yn=16777216,pa(t),yn=l):pa(t));break;default:pa(t)}}function Sr(t){var l=t.alternate;if(l!==null&&(t=l.child,t!==null)){l.child=null;do l=t.sibling,t.sibling=null,t=l;while(t!==null)}}function gn(t){var l=t.deletions;if((t.flags&16)!==0){if(l!==null)for(var e=0;e<l.length;e++){var a=l[e];kt=a,Ar(a,t)}Sr(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)xr(t),t=t.sibling}function xr(t){switch(t.tag){case 0:case 11:case 15:gn(t),t.flags&2048&&re(9,t,t.return);break;case 3:gn(t);break;case 12:gn(t);break;case 22:var l=t.stateNode;t.memoizedState!==null&&l._visibility&2&&(t.return===null||t.return.tag!==13)?(l._visibility&=-3,zu(t)):gn(t);break;default:gn(t)}}function zu(t){var l=t.deletions;if((t.flags&16)!==0){if(l!==null)for(var e=0;e<l.length;e++){var a=l[e];kt=a,Ar(a,t)}Sr(t)}for(t=t.child;t!==null;){switch(l=t,l.tag){case 0:case 11:case 15:re(8,l,l.return),zu(l);break;case 22:e=l.stateNode,e._visibility&2&&(e._visibility&=-3,zu(l));break;default:zu(l)}t=t.sibling}}function Ar(t,l){for(;kt!==null;){var e=kt;switch(e.tag){case 0:case 11:case 15:re(8,e,l);break;case 23:case 22:if(e.memoizedState!==null&&e.memoizedState.cachePool!==null){var a=e.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Pa(e.memoizedState.cache)}if(a=e.child,a!==null)a.return=e,kt=a;else t:for(e=t;kt!==null;){a=kt;var n=a.sibling,u=a.return;if(mr(a),a===e){kt=null;break t}if(n!==null){n.return=u,kt=n;break t}kt=u}}}var wh={getCacheForType:function(t){var l=$t(wt),e=l.data.get(t);return e===void 0&&(e=t(),l.data.set(t,e)),e}},Yh=typeof WeakMap=="function"?WeakMap:Map,yt=0,zt=null,it=null,st=0,gt=0,ml=null,he=!1,Sa=!1,Rc=!1,Jl=0,jt=0,ve=0,Qe=0,Uc=0,El=0,xa=0,bn=null,nl=null,jc=!1,Hc=0,Mu=1/0,_u=null,ye=null,Lt=0,ge=null,Aa=null,Ea=0,Cc=0,qc=null,Er=null,pn=0,Bc=null;function hl(){if((yt&2)!==0&&st!==0)return st&-st;if(T.T!==null){var t=oa;return t!==0?t:Zc()}return wf()}function Tr(){El===0&&(El=(st&536870912)===0||ht?Hf():536870912);var t=Al.current;return t!==null&&(t.flags|=32),El}function vl(t,l,e){(t===zt&&(gt===2||gt===9)||t.cancelPendingCommit!==null)&&(Ta(t,0),be(t,st,El,!1)),qa(t,e),((yt&2)===0||t!==zt)&&(t===zt&&((yt&2)===0&&(Qe|=e),jt===4&&be(t,st,El,!1)),Hl(t))}function zr(t,l,e){if((yt&6)!==0)throw Error(s(327));var a=!e&&(l&124)===0&&(l&t.expiredLanes)===0||Ca(t,l),n=a?Qh(t,l):Gc(t,l,!0),u=a;do{if(n===0){Sa&&!a&&be(t,l,0,!1);break}else{if(e=t.current.alternate,u&&!Gh(e)){n=Gc(t,l,!1),u=!1;continue}if(n===2){if(u=l,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){l=i;t:{var c=t;n=bn;var o=c.current.memoizedState.isDehydrated;if(o&&(Ta(c,i).flags|=256),i=Gc(c,i,!1),i!==2){if(Rc&&!o){c.errorRecoveryDisabledLanes|=u,Qe|=u,n=4;break t}u=nl,nl=n,u!==null&&(nl===null?nl=u:nl.push.apply(nl,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Ta(t,0),be(t,l,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(s(345));case 4:if((l&4194048)!==l)break;case 6:be(a,l,El,!he);break t;case 2:nl=null;break;case 3:case 5:break;default:throw Error(s(329))}if((l&62914560)===l&&(n=Hc+300-Dl(),10<n)){if(be(a,l,El,!he),wn(a,0,!0)!==0)break t;a.timeoutHandle=td(Mr.bind(null,a,e,nl,_u,jc,l,El,Qe,xa,he,u,2,-0,0),n);break t}Mr(a,e,nl,_u,jc,l,El,Qe,xa,he,u,0,-0,0)}}break}while(!0);Hl(t)}function Mr(t,l,e,a,n,u,i,c,o,b,z,O,S,x){if(t.timeoutHandle=-1,O=l.subtreeFlags,(O&8192||(O&16785408)===16785408)&&(Mn={stylesheets:null,count:0,unsuspend:x0},pr(l),O=E0(),O!==null)){t.cancelPendingCommit=O(jr.bind(null,t,l,u,e,a,n,i,c,o,z,1,S,x)),be(t,u,i,!b);return}jr(t,l,u,e,a,n,i,c,o)}function Gh(t){for(var l=t;;){var e=l.tag;if((e===0||e===11||e===15)&&l.flags&16384&&(e=l.updateQueue,e!==null&&(e=e.stores,e!==null)))for(var a=0;a<e.length;a++){var n=e[a],u=n.getSnapshot;n=n.value;try{if(!sl(u(),n))return!1}catch{return!1}}if(e=l.child,l.subtreeFlags&16384&&e!==null)e.return=l,l=e;else{if(l===t)break;for(;l.sibling===null;){if(l.return===null||l.return===t)return!0;l=l.return}l.sibling.return=l.return,l=l.sibling}}return!0}function be(t,l,e,a){l&=~Uc,l&=~Qe,t.suspendedLanes|=l,t.pingedLanes&=~l,a&&(t.warmLanes|=l),a=t.expirationTimes;for(var n=l;0<n;){var u=31-fl(n),i=1<<u;a[u]=-1,n&=~i}e!==0&&qf(t,e,l)}function Ou(){return(yt&6)===0?(Sn(0),!1):!0}function wc(){if(it!==null){if(gt===0)var t=it.return;else t=it,Gl=qe=null,lc(t),ya=null,on=0,t=it;for(;t!==null;)ur(t.alternate,t),t=t.return;it=null}}function Ta(t,l){var e=t.timeoutHandle;e!==-1&&(t.timeoutHandle=-1,n0(e)),e=t.cancelPendingCommit,e!==null&&(t.cancelPendingCommit=null,e()),wc(),zt=t,it=e=Bl(t.current,null),st=l,gt=0,ml=null,he=!1,Sa=Ca(t,l),Rc=!1,xa=El=Uc=Qe=ve=jt=0,nl=bn=null,jc=!1,(l&8)!==0&&(l|=l&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=l;0<a;){var n=31-fl(a),u=1<<n;l|=t[n],a&=~u}return Jl=l,Wn(),e}function _r(t,l){tt=null,T.H=vu,l===tn||l===uu?(l=ks(),gt=3):l===Gs?(l=ks(),gt=4):gt=l===Vo?8:l!==null&&typeof l=="object"&&typeof l.then=="function"?6:1,ml=l,it===null&&(jt=1,Su(t,bl(l,t.current)))}function Or(){var t=T.H;return T.H=vu,t===null?vu:t}function Dr(){var t=T.A;return T.A=wh,t}function Yc(){jt=4,he||(st&4194048)!==st&&Al.current!==null||(Sa=!0),(ve&134217727)===0&&(Qe&134217727)===0||zt===null||be(zt,st,El,!1)}function Gc(t,l,e){var a=yt;yt|=2;var n=Or(),u=Dr();(zt!==t||st!==l)&&(_u=null,Ta(t,l)),l=!1;var i=jt;t:do try{if(gt!==0&&it!==null){var c=it,o=ml;switch(gt){case 8:wc(),i=6;break t;case 3:case 2:case 9:case 6:Al.current===null&&(l=!0);var b=gt;if(gt=0,ml=null,za(t,c,o,b),e&&Sa){i=0;break t}break;default:b=gt,gt=0,ml=null,za(t,c,o,b)}}Xh(),i=jt;break}catch(z){_r(t,z)}while(!0);return l&&t.shellSuspendCounter++,Gl=qe=null,yt=a,T.H=n,T.A=u,it===null&&(zt=null,st=0,Wn()),i}function Xh(){for(;it!==null;)Nr(it)}function Qh(t,l){var e=yt;yt|=2;var a=Or(),n=Dr();zt!==t||st!==l?(_u=null,Mu=Dl()+500,Ta(t,l)):Sa=Ca(t,l);t:do try{if(gt!==0&&it!==null){l=it;var u=ml;l:switch(gt){case 1:gt=0,ml=null,za(t,l,u,1);break;case 2:case 9:if(Xs(u)){gt=0,ml=null,Rr(l);break}l=function(){gt!==2&&gt!==9||zt!==t||(gt=7),Hl(t)},u.then(l,l);break t;case 3:gt=7;break t;case 4:gt=5;break t;case 7:Xs(u)?(gt=0,ml=null,Rr(l)):(gt=0,ml=null,za(t,l,u,7));break;case 5:var i=null;switch(it.tag){case 26:i=it.memoizedState;case 5:case 27:var c=it;if(!i||dd(i)){gt=0,ml=null;var o=c.sibling;if(o!==null)it=o;else{var b=c.return;b!==null?(it=b,Du(b)):it=null}break l}}gt=0,ml=null,za(t,l,u,5);break;case 6:gt=0,ml=null,za(t,l,u,6);break;case 8:wc(),jt=6;break t;default:throw Error(s(462))}}kh();break}catch(z){_r(t,z)}while(!0);return Gl=qe=null,T.H=a,T.A=n,yt=e,it!==null?0:(zt=null,st=0,Wn(),jt)}function kh(){for(;it!==null&&!rm();)Nr(it)}function Nr(t){var l=ar(t.alternate,t,Jl);t.memoizedProps=t.pendingProps,l===null?Du(t):it=l}function Rr(t){var l=t,e=l.alternate;switch(l.tag){case 15:case 0:l=Fo(e,l,l.pendingProps,l.type,void 0,st);break;case 11:l=Fo(e,l,l.pendingProps,l.type.render,l.ref,st);break;case 5:lc(l);default:ur(e,l),l=it=Rs(l,Jl),l=ar(e,l,Jl)}t.memoizedProps=t.pendingProps,l===null?Du(t):it=l}function za(t,l,e,a){Gl=qe=null,lc(l),ya=null,on=0;var n=l.return;try{if(Uh(t,n,l,e,st)){jt=1,Su(t,bl(e,t.current)),it=null;return}}catch(u){if(n!==null)throw it=n,u;jt=1,Su(t,bl(e,t.current)),it=null;return}l.flags&32768?(ht||a===1?t=!0:Sa||(st&536870912)!==0?t=!1:(he=t=!0,(a===2||a===9||a===3||a===6)&&(a=Al.current,a!==null&&a.tag===13&&(a.flags|=16384))),Ur(l,t)):Du(l)}function Du(t){var l=t;do{if((l.flags&32768)!==0){Ur(l,he);return}t=l.return;var e=Hh(l.alternate,l,Jl);if(e!==null){it=e;return}if(l=l.sibling,l!==null){it=l;return}it=l=t}while(l!==null);jt===0&&(jt=5)}function Ur(t,l){do{var e=Ch(t.alternate,t);if(e!==null){e.flags&=32767,it=e;return}if(e=t.return,e!==null&&(e.flags|=32768,e.subtreeFlags=0,e.deletions=null),!l&&(t=t.sibling,t!==null)){it=t;return}it=t=e}while(t!==null);jt=6,it=null}function jr(t,l,e,a,n,u,i,c,o){t.cancelPendingCommit=null;do Nu();while(Lt!==0);if((yt&6)!==0)throw Error(s(327));if(l!==null){if(l===t.current)throw Error(s(177));if(u=l.lanes|l.childLanes,u|=Ni,xm(t,e,u,i,c,o),t===zt&&(it=zt=null,st=0),Aa=l,ge=t,Ea=e,Cc=u,qc=n,Er=a,(l.subtreeFlags&10256)!==0||(l.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Kh(Cn,function(){return wr(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(l.flags&13878)!==0,(l.subtreeFlags&13878)!==0||a){a=T.T,T.T=null,n=H.p,H.p=2,i=yt,yt|=4;try{qh(t,l,e)}finally{yt=i,H.p=n,T.T=a}}Lt=1,Hr(),Cr(),qr()}}function Hr(){if(Lt===1){Lt=0;var t=ge,l=Aa,e=(l.flags&13878)!==0;if((l.subtreeFlags&13878)!==0||e){e=T.T,T.T=null;var a=H.p;H.p=2;var n=yt;yt|=4;try{yr(l,t);var u=Pc,i=xs(t.containerInfo),c=u.focusedElem,o=u.selectionRange;if(i!==c&&c&&c.ownerDocument&&Ss(c.ownerDocument.documentElement,c)){if(o!==null&&zi(c)){var b=o.start,z=o.end;if(z===void 0&&(z=b),"selectionStart"in c)c.selectionStart=b,c.selectionEnd=Math.min(z,c.value.length);else{var O=c.ownerDocument||document,S=O&&O.defaultView||window;if(S.getSelection){var x=S.getSelection(),$=c.textContent.length,K=Math.min(o.start,$),St=o.end===void 0?K:Math.min(o.end,$);!x.extend&&K>St&&(i=St,St=K,K=i);var v=ps(c,K),d=ps(c,St);if(v&&d&&(x.rangeCount!==1||x.anchorNode!==v.node||x.anchorOffset!==v.offset||x.focusNode!==d.node||x.focusOffset!==d.offset)){var y=O.createRange();y.setStart(v.node,v.offset),x.removeAllRanges(),K>St?(x.addRange(y),x.extend(d.node,d.offset)):(y.setEnd(d.node,d.offset),x.addRange(y))}}}}for(O=[],x=c;x=x.parentNode;)x.nodeType===1&&O.push({element:x,left:x.scrollLeft,top:x.scrollTop});for(typeof c.focus=="function"&&c.focus(),c=0;c<O.length;c++){var _=O[c];_.element.scrollLeft=_.left,_.element.scrollTop=_.top}}Qu=!!Fc,Pc=Fc=null}finally{yt=n,H.p=a,T.T=e}}t.current=l,Lt=2}}function Cr(){if(Lt===2){Lt=0;var t=ge,l=Aa,e=(l.flags&8772)!==0;if((l.subtreeFlags&8772)!==0||e){e=T.T,T.T=null;var a=H.p;H.p=2;var n=yt;yt|=4;try{dr(t,l.alternate,l)}finally{yt=n,H.p=a,T.T=e}}Lt=3}}function qr(){if(Lt===4||Lt===3){Lt=0,dm();var t=ge,l=Aa,e=Ea,a=Er;(l.subtreeFlags&10256)!==0||(l.flags&10256)!==0?Lt=5:(Lt=0,Aa=ge=null,Br(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(ye=null),ni(e),l=l.stateNode,cl&&typeof cl.onCommitFiberRoot=="function")try{cl.onCommitFiberRoot(Ha,l,void 0,(l.current.flags&128)===128)}catch{}if(a!==null){l=T.T,n=H.p,H.p=2,T.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var c=a[i];u(c.value,{componentStack:c.stack})}}finally{T.T=l,H.p=n}}(Ea&3)!==0&&Nu(),Hl(t),n=t.pendingLanes,(e&4194090)!==0&&(n&42)!==0?t===Bc?pn++:(pn=0,Bc=t):pn=0,Sn(0)}}function Br(t,l){(t.pooledCacheLanes&=l)===0&&(l=t.pooledCache,l!=null&&(t.pooledCache=null,Pa(l)))}function Nu(t){return Hr(),Cr(),qr(),wr()}function wr(){if(Lt!==5)return!1;var t=ge,l=Cc;Cc=0;var e=ni(Ea),a=T.T,n=H.p;try{H.p=32>e?32:e,T.T=null,e=qc,qc=null;var u=ge,i=Ea;if(Lt=0,Aa=ge=null,Ea=0,(yt&6)!==0)throw Error(s(331));var c=yt;if(yt|=4,xr(u.current),br(u,u.current,i,e),yt=c,Sn(0,!1),cl&&typeof cl.onPostCommitFiberRoot=="function")try{cl.onPostCommitFiberRoot(Ha,u)}catch{}return!0}finally{H.p=n,T.T=a,Br(t,l)}}function Yr(t,l,e){l=bl(e,l),l=vc(t.stateNode,l,2),t=ce(t,l,2),t!==null&&(qa(t,2),Hl(t))}function At(t,l,e){if(t.tag===3)Yr(t,t,e);else for(;l!==null;){if(l.tag===3){Yr(l,t,e);break}else if(l.tag===1){var a=l.stateNode;if(typeof l.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ye===null||!ye.has(a))){t=bl(e,t),e=ko(2),a=ce(l,e,2),a!==null&&(Zo(e,a,l,t),qa(a,2),Hl(a));break}}l=l.return}}function Xc(t,l,e){var a=t.pingCache;if(a===null){a=t.pingCache=new Yh;var n=new Set;a.set(l,n)}else n=a.get(l),n===void 0&&(n=new Set,a.set(l,n));n.has(e)||(Rc=!0,n.add(e),t=Zh.bind(null,t,l,e),l.then(t,t))}function Zh(t,l,e){var a=t.pingCache;a!==null&&a.delete(l),t.pingedLanes|=t.suspendedLanes&e,t.warmLanes&=~e,zt===t&&(st&e)===e&&(jt===4||jt===3&&(st&62914560)===st&&300>Dl()-Hc?(yt&2)===0&&Ta(t,0):Uc|=e,xa===st&&(xa=0)),Hl(t)}function Gr(t,l){l===0&&(l=Cf()),t=ia(t,l),t!==null&&(qa(t,l),Hl(t))}function Vh(t){var l=t.memoizedState,e=0;l!==null&&(e=l.retryLane),Gr(t,e)}function Lh(t,l){var e=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(e=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(l),Gr(t,e)}function Kh(t,l){return ti(t,l)}var Ru=null,Ma=null,Qc=!1,Uu=!1,kc=!1,ke=0;function Hl(t){t!==Ma&&t.next===null&&(Ma===null?Ru=Ma=t:Ma=Ma.next=t),Uu=!0,Qc||(Qc=!0,$h())}function Sn(t,l){if(!kc&&Uu){kc=!0;do for(var e=!1,a=Ru;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,c=a.pingedLanes;u=(1<<31-fl(42|t)+1)-1,u&=n&~(i&~c),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(e=!0,Zr(a,u))}else u=st,u=wn(a,a===zt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Ca(a,u)||(e=!0,Zr(a,u));a=a.next}while(e);kc=!1}}function Jh(){Xr()}function Xr(){Uu=Qc=!1;var t=0;ke!==0&&(a0()&&(t=ke),ke=0);for(var l=Dl(),e=null,a=Ru;a!==null;){var n=a.next,u=Qr(a,l);u===0?(a.next=null,e===null?Ru=n:e.next=n,n===null&&(Ma=e)):(e=a,(t!==0||(u&3)!==0)&&(Uu=!0)),a=n}Sn(t)}function Qr(t,l){for(var e=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-fl(u),c=1<<i,o=n[i];o===-1?((c&e)===0||(c&a)!==0)&&(n[i]=Sm(c,l)):o<=l&&(t.expiredLanes|=c),u&=~c}if(l=zt,e=st,e=wn(t,t===l?e:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,e===0||t===l&&(gt===2||gt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&li(a),t.callbackNode=null,t.callbackPriority=0;if((e&3)===0||Ca(t,e)){if(l=e&-e,l===t.callbackPriority)return l;switch(a!==null&&li(a),ni(e)){case 2:case 8:e=Uf;break;case 32:e=Cn;break;case 268435456:e=jf;break;default:e=Cn}return a=kr.bind(null,t),e=ti(e,a),t.callbackPriority=l,t.callbackNode=e,l}return a!==null&&a!==null&&li(a),t.callbackPriority=2,t.callbackNode=null,2}function kr(t,l){if(Lt!==0&&Lt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var e=t.callbackNode;if(Nu()&&t.callbackNode!==e)return null;var a=st;return a=wn(t,t===zt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(zr(t,a,l),Qr(t,Dl()),t.callbackNode!=null&&t.callbackNode===e?kr.bind(null,t):null)}function Zr(t,l){if(Nu())return null;zr(t,l,!0)}function $h(){u0(function(){(yt&6)!==0?ti(Rf,Jh):Xr()})}function Zc(){return ke===0&&(ke=Hf()),ke}function Vr(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:kn(""+t)}function Lr(t,l){var e=l.ownerDocument.createElement("input");return e.name=l.name,e.value=l.value,t.id&&e.setAttribute("form",t.id),l.parentNode.insertBefore(e,l),t=new FormData(t),e.parentNode.removeChild(e),t}function Wh(t,l,e,a,n){if(l==="submit"&&e&&e.stateNode===n){var u=Vr((n[tl]||null).action),i=a.submitter;i&&(l=(l=i[tl]||null)?Vr(l.formAction):i.getAttribute("formAction"),l!==null&&(u=l,i=null));var c=new Kn("action","action",null,a,n);t.push({event:c,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ke!==0){var o=i?Lr(n,i):new FormData(n);oc(e,{pending:!0,data:o,method:n.method,action:u},null,o)}}else typeof u=="function"&&(c.preventDefault(),o=i?Lr(n,i):new FormData(n),oc(e,{pending:!0,data:o,method:n.method,action:u},u,o))},currentTarget:n}]})}}for(var Vc=0;Vc<Di.length;Vc++){var Lc=Di[Vc],Fh=Lc.toLowerCase(),Ph=Lc[0].toUpperCase()+Lc.slice(1);Ml(Fh,"on"+Ph)}Ml(Ts,"onAnimationEnd"),Ml(zs,"onAnimationIteration"),Ml(Ms,"onAnimationStart"),Ml("dblclick","onDoubleClick"),Ml("focusin","onFocus"),Ml("focusout","onBlur"),Ml(vh,"onTransitionRun"),Ml(yh,"onTransitionStart"),Ml(gh,"onTransitionCancel"),Ml(_s,"onTransitionEnd"),We("onMouseEnter",["mouseout","mouseover"]),We("onMouseLeave",["mouseout","mouseover"]),We("onPointerEnter",["pointerout","pointerover"]),We("onPointerLeave",["pointerout","pointerover"]),_e("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),_e("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),_e("onBeforeInput",["compositionend","keypress","textInput","paste"]),_e("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),_e("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),_e("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ih=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(xn));function Kr(t,l){l=(l&4)!==0;for(var e=0;e<t.length;e++){var a=t[e],n=a.event;a=a.listeners;t:{var u=void 0;if(l)for(var i=a.length-1;0<=i;i--){var c=a[i],o=c.instance,b=c.currentTarget;if(c=c.listener,o!==u&&n.isPropagationStopped())break t;u=c,n.currentTarget=b;try{u(n)}catch(z){pu(z)}n.currentTarget=null,u=o}else for(i=0;i<a.length;i++){if(c=a[i],o=c.instance,b=c.currentTarget,c=c.listener,o!==u&&n.isPropagationStopped())break t;u=c,n.currentTarget=b;try{u(n)}catch(z){pu(z)}n.currentTarget=null,u=o}}}}function ct(t,l){var e=l[ui];e===void 0&&(e=l[ui]=new Set);var a=t+"__bubble";e.has(a)||(Jr(l,t,2,!1),e.add(a))}function Kc(t,l,e){var a=0;l&&(a|=4),Jr(e,t,a,l)}var ju="_reactListening"+Math.random().toString(36).slice(2);function Jc(t){if(!t[ju]){t[ju]=!0,Gf.forEach(function(e){e!=="selectionchange"&&(Ih.has(e)||Kc(e,!1,t),Kc(e,!0,t))});var l=t.nodeType===9?t:t.ownerDocument;l===null||l[ju]||(l[ju]=!0,Kc("selectionchange",!1,l))}}function Jr(t,l,e,a){switch(bd(l)){case 2:var n=M0;break;case 8:n=_0;break;default:n=sf}e=n.bind(null,l,e,t),n=void 0,!yi||l!=="touchstart"&&l!=="touchmove"&&l!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(l,e,{capture:!0,passive:n}):t.addEventListener(l,e,!0):n!==void 0?t.addEventListener(l,e,{passive:n}):t.addEventListener(l,e,!1)}function $c(t,l,e,a,n){var u=a;if((l&1)===0&&(l&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var c=a.stateNode.containerInfo;if(c===n)break;if(i===4)for(i=a.return;i!==null;){var o=i.tag;if((o===3||o===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;c!==null;){if(i=Ke(c),i===null)return;if(o=i.tag,o===5||o===6||o===26||o===27){a=u=i;continue t}c=c.parentNode}}a=a.return}ts(function(){var b=u,z=hi(e),O=[];t:{var S=Os.get(t);if(S!==void 0){var x=Kn,$=t;switch(t){case"keypress":if(Vn(e)===0)break t;case"keydown":case"keyup":x=Km;break;case"focusin":$="focus",x=Si;break;case"focusout":$="blur",x=Si;break;case"beforeblur":case"afterblur":x=Si;break;case"click":if(e.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=as;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Cm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=Wm;break;case Ts:case zs:case Ms:x=wm;break;case _s:x=Pm;break;case"scroll":case"scrollend":x=jm;break;case"wheel":x=th;break;case"copy":case"cut":case"paste":x=Gm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=us;break;case"toggle":case"beforetoggle":x=eh}var K=(l&4)!==0,St=!K&&(t==="scroll"||t==="scrollend"),v=K?S!==null?S+"Capture":null:S;K=[];for(var d=b,y;d!==null;){var _=d;if(y=_.stateNode,_=_.tag,_!==5&&_!==26&&_!==27||y===null||v===null||(_=Ya(d,v),_!=null&&K.push(An(d,_,y))),St)break;d=d.return}0<K.length&&(S=new x(S,$,null,e,z),O.push({event:S,listeners:K}))}}if((l&7)===0){t:{if(S=t==="mouseover"||t==="pointerover",x=t==="mouseout"||t==="pointerout",S&&e!==mi&&($=e.relatedTarget||e.fromElement)&&(Ke($)||$[Le]))break t;if((x||S)&&(S=z.window===z?z:(S=z.ownerDocument)?S.defaultView||S.parentWindow:window,x?($=e.relatedTarget||e.toElement,x=b,$=$?Ke($):null,$!==null&&(St=E($),K=$.tag,$!==St||K!==5&&K!==27&&K!==6)&&($=null)):(x=null,$=b),x!==$)){if(K=as,_="onMouseLeave",v="onMouseEnter",d="mouse",(t==="pointerout"||t==="pointerover")&&(K=us,_="onPointerLeave",v="onPointerEnter",d="pointer"),St=x==null?S:wa(x),y=$==null?S:wa($),S=new K(_,d+"leave",x,e,z),S.target=St,S.relatedTarget=y,_=null,Ke(z)===b&&(K=new K(v,d+"enter",$,e,z),K.target=y,K.relatedTarget=St,_=K),St=_,x&&$)l:{for(K=x,v=$,d=0,y=K;y;y=_a(y))d++;for(y=0,_=v;_;_=_a(_))y++;for(;0<d-y;)K=_a(K),d--;for(;0<y-d;)v=_a(v),y--;for(;d--;){if(K===v||v!==null&&K===v.alternate)break l;K=_a(K),v=_a(v)}K=null}else K=null;x!==null&&$r(O,S,x,K,!1),$!==null&&St!==null&&$r(O,St,$,K,!0)}}t:{if(S=b?wa(b):window,x=S.nodeName&&S.nodeName.toLowerCase(),x==="select"||x==="input"&&S.type==="file")var w=ms;else if(rs(S))if(hs)w=dh;else{w=oh;var at=sh}else x=S.nodeName,!x||x.toLowerCase()!=="input"||S.type!=="checkbox"&&S.type!=="radio"?b&&di(b.elementType)&&(w=ms):w=rh;if(w&&(w=w(t,b))){ds(O,w,e,z);break t}at&&at(t,S,b),t==="focusout"&&b&&S.type==="number"&&b.memoizedProps.value!=null&&ri(S,"number",S.value)}switch(at=b?wa(b):window,t){case"focusin":(rs(at)||at.contentEditable==="true")&&(aa=at,Mi=b,Ka=null);break;case"focusout":Ka=Mi=aa=null;break;case"mousedown":_i=!0;break;case"contextmenu":case"mouseup":case"dragend":_i=!1,As(O,e,z);break;case"selectionchange":if(hh)break;case"keydown":case"keyup":As(O,e,z)}var Z;if(Ai)t:{switch(t){case"compositionstart":var J="onCompositionStart";break t;case"compositionend":J="onCompositionEnd";break t;case"compositionupdate":J="onCompositionUpdate";break t}J=void 0}else ea?ss(t,e)&&(J="onCompositionEnd"):t==="keydown"&&e.keyCode===229&&(J="onCompositionStart");J&&(is&&e.locale!=="ko"&&(ea||J!=="onCompositionStart"?J==="onCompositionEnd"&&ea&&(Z=ls()):(ae=z,gi="value"in ae?ae.value:ae.textContent,ea=!0)),at=Hu(b,J),0<at.length&&(J=new ns(J,t,null,e,z),O.push({event:J,listeners:at}),Z?J.data=Z:(Z=os(e),Z!==null&&(J.data=Z)))),(Z=nh?uh(t,e):ih(t,e))&&(J=Hu(b,"onBeforeInput"),0<J.length&&(at=new ns("onBeforeInput","beforeinput",null,e,z),O.push({event:at,listeners:J}),at.data=Z)),Wh(O,t,b,e,z)}Kr(O,l)})}function An(t,l,e){return{instance:t,listener:l,currentTarget:e}}function Hu(t,l){for(var e=l+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ya(t,e),n!=null&&a.unshift(An(t,n,u)),n=Ya(t,l),n!=null&&a.push(An(t,n,u))),t.tag===3)return a;t=t.return}return[]}function _a(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function $r(t,l,e,a,n){for(var u=l._reactName,i=[];e!==null&&e!==a;){var c=e,o=c.alternate,b=c.stateNode;if(c=c.tag,o!==null&&o===a)break;c!==5&&c!==26&&c!==27||b===null||(o=b,n?(b=Ya(e,u),b!=null&&i.unshift(An(e,b,o))):n||(b=Ya(e,u),b!=null&&i.push(An(e,b,o)))),e=e.return}i.length!==0&&t.push({event:l,listeners:i})}var t0=/\r\n?/g,l0=/\u0000|\uFFFD/g;function Wr(t){return(typeof t=="string"?t:""+t).replace(t0,`
`).replace(l0,"")}function Fr(t,l){return l=Wr(l),Wr(t)===l}function Cu(){}function pt(t,l,e,a,n,u){switch(e){case"children":typeof a=="string"?l==="body"||l==="textarea"&&a===""||Ie(t,a):(typeof a=="number"||typeof a=="bigint")&&l!=="body"&&Ie(t,""+a);break;case"className":Gn(t,"class",a);break;case"tabIndex":Gn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Gn(t,e,a);break;case"style":Pf(t,a,u);break;case"data":if(l!=="object"){Gn(t,"data",a);break}case"src":case"href":if(a===""&&(l!=="a"||e!=="href")){t.removeAttribute(e);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(e);break}a=kn(""+a),t.setAttribute(e,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(e,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(e==="formAction"?(l!=="input"&&pt(t,l,"name",n.name,n,null),pt(t,l,"formEncType",n.formEncType,n,null),pt(t,l,"formMethod",n.formMethod,n,null),pt(t,l,"formTarget",n.formTarget,n,null)):(pt(t,l,"encType",n.encType,n,null),pt(t,l,"method",n.method,n,null),pt(t,l,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(e);break}a=kn(""+a),t.setAttribute(e,a);break;case"onClick":a!=null&&(t.onclick=Cu);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(e=a.__html,e!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=e}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}e=kn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",e);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(e,""+a):t.removeAttribute(e);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(e,""):t.removeAttribute(e);break;case"capture":case"download":a===!0?t.setAttribute(e,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(e,a):t.removeAttribute(e);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(e,a):t.removeAttribute(e);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(e):t.setAttribute(e,a);break;case"popover":ct("beforetoggle",t),ct("toggle",t),Yn(t,"popover",a);break;case"xlinkActuate":Cl(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Cl(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Cl(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Cl(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Cl(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Cl(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Cl(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Cl(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Cl(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Yn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(e=Rm.get(e)||e,Yn(t,e,a))}}function Wc(t,l,e,a,n,u){switch(e){case"style":Pf(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(e=a.__html,e!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=e}}break;case"children":typeof a=="string"?Ie(t,a):(typeof a=="number"||typeof a=="bigint")&&Ie(t,""+a);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Cu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Xf.hasOwnProperty(e))t:{if(e[0]==="o"&&e[1]==="n"&&(n=e.endsWith("Capture"),l=e.slice(2,n?e.length-7:void 0),u=t[tl]||null,u=u!=null?u[e]:null,typeof u=="function"&&t.removeEventListener(l,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(e in t?t[e]=null:t.hasAttribute(e)&&t.removeAttribute(e)),t.addEventListener(l,a,n);break t}e in t?t[e]=a:a===!0?t.setAttribute(e,""):Yn(t,e,a)}}}function Kt(t,l,e){switch(l){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ct("error",t),ct("load",t);var a=!1,n=!1,u;for(u in e)if(e.hasOwnProperty(u)){var i=e[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,l));default:pt(t,l,u,i,e,null)}}n&&pt(t,l,"srcSet",e.srcSet,e,null),a&&pt(t,l,"src",e.src,e,null);return;case"input":ct("invalid",t);var c=u=i=n=null,o=null,b=null;for(a in e)if(e.hasOwnProperty(a)){var z=e[a];if(z!=null)switch(a){case"name":n=z;break;case"type":i=z;break;case"checked":o=z;break;case"defaultChecked":b=z;break;case"value":u=z;break;case"defaultValue":c=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(s(137,l));break;default:pt(t,l,a,z,e,null)}}Jf(t,u,c,o,b,i,n,!1),Xn(t);return;case"select":ct("invalid",t),a=i=u=null;for(n in e)if(e.hasOwnProperty(n)&&(c=e[n],c!=null))switch(n){case"value":u=c;break;case"defaultValue":i=c;break;case"multiple":a=c;default:pt(t,l,n,c,e,null)}l=u,e=i,t.multiple=!!a,l!=null?Pe(t,!!a,l,!1):e!=null&&Pe(t,!!a,e,!0);return;case"textarea":ct("invalid",t),u=n=a=null;for(i in e)if(e.hasOwnProperty(i)&&(c=e[i],c!=null))switch(i){case"value":a=c;break;case"defaultValue":n=c;break;case"children":u=c;break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(s(91));break;default:pt(t,l,i,c,e,null)}Wf(t,a,n,u),Xn(t);return;case"option":for(o in e)if(e.hasOwnProperty(o)&&(a=e[o],a!=null))switch(o){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:pt(t,l,o,a,e,null)}return;case"dialog":ct("beforetoggle",t),ct("toggle",t),ct("cancel",t),ct("close",t);break;case"iframe":case"object":ct("load",t);break;case"video":case"audio":for(a=0;a<xn.length;a++)ct(xn[a],t);break;case"image":ct("error",t),ct("load",t);break;case"details":ct("toggle",t);break;case"embed":case"source":case"link":ct("error",t),ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(b in e)if(e.hasOwnProperty(b)&&(a=e[b],a!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,l));default:pt(t,l,b,a,e,null)}return;default:if(di(l)){for(z in e)e.hasOwnProperty(z)&&(a=e[z],a!==void 0&&Wc(t,l,z,a,e,void 0));return}}for(c in e)e.hasOwnProperty(c)&&(a=e[c],a!=null&&pt(t,l,c,a,e,null))}function e0(t,l,e,a){switch(l){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,c=null,o=null,b=null,z=null;for(x in e){var O=e[x];if(e.hasOwnProperty(x)&&O!=null)switch(x){case"checked":break;case"value":break;case"defaultValue":o=O;default:a.hasOwnProperty(x)||pt(t,l,x,null,a,O)}}for(var S in a){var x=a[S];if(O=e[S],a.hasOwnProperty(S)&&(x!=null||O!=null))switch(S){case"type":u=x;break;case"name":n=x;break;case"checked":b=x;break;case"defaultChecked":z=x;break;case"value":i=x;break;case"defaultValue":c=x;break;case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(s(137,l));break;default:x!==O&&pt(t,l,S,x,a,O)}}oi(t,i,c,o,b,z,u,n);return;case"select":x=i=c=S=null;for(u in e)if(o=e[u],e.hasOwnProperty(u)&&o!=null)switch(u){case"value":break;case"multiple":x=o;default:a.hasOwnProperty(u)||pt(t,l,u,null,a,o)}for(n in a)if(u=a[n],o=e[n],a.hasOwnProperty(n)&&(u!=null||o!=null))switch(n){case"value":S=u;break;case"defaultValue":c=u;break;case"multiple":i=u;default:u!==o&&pt(t,l,n,u,a,o)}l=c,e=i,a=x,S!=null?Pe(t,!!e,S,!1):!!a!=!!e&&(l!=null?Pe(t,!!e,l,!0):Pe(t,!!e,e?[]:"",!1));return;case"textarea":x=S=null;for(c in e)if(n=e[c],e.hasOwnProperty(c)&&n!=null&&!a.hasOwnProperty(c))switch(c){case"value":break;case"children":break;default:pt(t,l,c,null,a,n)}for(i in a)if(n=a[i],u=e[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":S=n;break;case"defaultValue":x=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==u&&pt(t,l,i,n,a,u)}$f(t,S,x);return;case"option":for(var $ in e)if(S=e[$],e.hasOwnProperty($)&&S!=null&&!a.hasOwnProperty($))switch($){case"selected":t.selected=!1;break;default:pt(t,l,$,null,a,S)}for(o in a)if(S=a[o],x=e[o],a.hasOwnProperty(o)&&S!==x&&(S!=null||x!=null))switch(o){case"selected":t.selected=S&&typeof S!="function"&&typeof S!="symbol";break;default:pt(t,l,o,S,a,x)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var K in e)S=e[K],e.hasOwnProperty(K)&&S!=null&&!a.hasOwnProperty(K)&&pt(t,l,K,null,a,S);for(b in a)if(S=a[b],x=e[b],a.hasOwnProperty(b)&&S!==x&&(S!=null||x!=null))switch(b){case"children":case"dangerouslySetInnerHTML":if(S!=null)throw Error(s(137,l));break;default:pt(t,l,b,S,a,x)}return;default:if(di(l)){for(var St in e)S=e[St],e.hasOwnProperty(St)&&S!==void 0&&!a.hasOwnProperty(St)&&Wc(t,l,St,void 0,a,S);for(z in a)S=a[z],x=e[z],!a.hasOwnProperty(z)||S===x||S===void 0&&x===void 0||Wc(t,l,z,S,a,x);return}}for(var v in e)S=e[v],e.hasOwnProperty(v)&&S!=null&&!a.hasOwnProperty(v)&&pt(t,l,v,null,a,S);for(O in a)S=a[O],x=e[O],!a.hasOwnProperty(O)||S===x||S==null&&x==null||pt(t,l,O,S,a,x)}var Fc=null,Pc=null;function qu(t){return t.nodeType===9?t:t.ownerDocument}function Pr(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ir(t,l){if(t===0)switch(l){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&l==="foreignObject"?0:t}function Ic(t,l){return t==="textarea"||t==="noscript"||typeof l.children=="string"||typeof l.children=="number"||typeof l.children=="bigint"||typeof l.dangerouslySetInnerHTML=="object"&&l.dangerouslySetInnerHTML!==null&&l.dangerouslySetInnerHTML.__html!=null}var tf=null;function a0(){var t=window.event;return t&&t.type==="popstate"?t===tf?!1:(tf=t,!0):(tf=null,!1)}var td=typeof setTimeout=="function"?setTimeout:void 0,n0=typeof clearTimeout=="function"?clearTimeout:void 0,ld=typeof Promise=="function"?Promise:void 0,u0=typeof queueMicrotask=="function"?queueMicrotask:typeof ld<"u"?function(t){return ld.resolve(null).then(t).catch(i0)}:td;function i0(t){setTimeout(function(){throw t})}function pe(t){return t==="head"}function ed(t,l){var e=l,a=0,n=0;do{var u=e.nextSibling;if(t.removeChild(e),u&&u.nodeType===8)if(e=u.data,e==="/$"){if(0<a&&8>a){e=a;var i=t.ownerDocument;if(e&1&&En(i.documentElement),e&2&&En(i.body),e&4)for(e=i.head,En(e),i=e.firstChild;i;){var c=i.nextSibling,o=i.nodeName;i[Ba]||o==="SCRIPT"||o==="STYLE"||o==="LINK"&&i.rel.toLowerCase()==="stylesheet"||e.removeChild(i),i=c}}if(n===0){t.removeChild(u),Rn(l);return}n--}else e==="$"||e==="$?"||e==="$!"?n++:a=e.charCodeAt(0)-48;else a=0;e=u}while(e);Rn(l)}function lf(t){var l=t.firstChild;for(l&&l.nodeType===10&&(l=l.nextSibling);l;){var e=l;switch(l=l.nextSibling,e.nodeName){case"HTML":case"HEAD":case"BODY":lf(e),ii(e);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(e.rel.toLowerCase()==="stylesheet")continue}t.removeChild(e)}}function c0(t,l,e,a){for(;t.nodeType===1;){var n=e;if(t.nodeName.toLowerCase()!==l.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Ba])switch(l){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(l==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Ol(t.nextSibling),t===null)break}return null}function f0(t,l,e){if(l==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!e||(t=Ol(t.nextSibling),t===null))return null;return t}function ef(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function s0(t,l){var e=t.ownerDocument;if(t.data!=="$?"||e.readyState==="complete")l();else{var a=function(){l(),e.removeEventListener("DOMContentLoaded",a)};e.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Ol(t){for(;t!=null;t=t.nextSibling){var l=t.nodeType;if(l===1||l===3)break;if(l===8){if(l=t.data,l==="$"||l==="$!"||l==="$?"||l==="F!"||l==="F")break;if(l==="/$")return null}}return t}var af=null;function ad(t){t=t.previousSibling;for(var l=0;t;){if(t.nodeType===8){var e=t.data;if(e==="$"||e==="$!"||e==="$?"){if(l===0)return t;l--}else e==="/$"&&l++}t=t.previousSibling}return null}function nd(t,l,e){switch(l=qu(e),t){case"html":if(t=l.documentElement,!t)throw Error(s(452));return t;case"head":if(t=l.head,!t)throw Error(s(453));return t;case"body":if(t=l.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function En(t){for(var l=t.attributes;l.length;)t.removeAttributeNode(l[0]);ii(t)}var Tl=new Map,ud=new Set;function Bu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var $l=H.d;H.d={f:o0,r:r0,D:d0,C:m0,L:h0,m:v0,X:g0,S:y0,M:b0};function o0(){var t=$l.f(),l=Ou();return t||l}function r0(t){var l=Je(t);l!==null&&l.tag===5&&l.type==="form"?zo(l):$l.r(t)}var Oa=typeof document>"u"?null:document;function id(t,l,e){var a=Oa;if(a&&typeof l=="string"&&l){var n=gl(l);n='link[rel="'+t+'"][href="'+n+'"]',typeof e=="string"&&(n+='[crossorigin="'+e+'"]'),ud.has(n)||(ud.add(n),t={rel:t,crossOrigin:e,href:l},a.querySelector(n)===null&&(l=a.createElement("link"),Kt(l,"link",t),Xt(l),a.head.appendChild(l)))}}function d0(t){$l.D(t),id("dns-prefetch",t,null)}function m0(t,l){$l.C(t,l),id("preconnect",t,l)}function h0(t,l,e){$l.L(t,l,e);var a=Oa;if(a&&t&&l){var n='link[rel="preload"][as="'+gl(l)+'"]';l==="image"&&e&&e.imageSrcSet?(n+='[imagesrcset="'+gl(e.imageSrcSet)+'"]',typeof e.imageSizes=="string"&&(n+='[imagesizes="'+gl(e.imageSizes)+'"]')):n+='[href="'+gl(t)+'"]';var u=n;switch(l){case"style":u=Da(t);break;case"script":u=Na(t)}Tl.has(u)||(t=N({rel:"preload",href:l==="image"&&e&&e.imageSrcSet?void 0:t,as:l},e),Tl.set(u,t),a.querySelector(n)!==null||l==="style"&&a.querySelector(Tn(u))||l==="script"&&a.querySelector(zn(u))||(l=a.createElement("link"),Kt(l,"link",t),Xt(l),a.head.appendChild(l)))}}function v0(t,l){$l.m(t,l);var e=Oa;if(e&&t){var a=l&&typeof l.as=="string"?l.as:"script",n='link[rel="modulepreload"][as="'+gl(a)+'"][href="'+gl(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Na(t)}if(!Tl.has(u)&&(t=N({rel:"modulepreload",href:t},l),Tl.set(u,t),e.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(e.querySelector(zn(u)))return}a=e.createElement("link"),Kt(a,"link",t),Xt(a),e.head.appendChild(a)}}}function y0(t,l,e){$l.S(t,l,e);var a=Oa;if(a&&t){var n=$e(a).hoistableStyles,u=Da(t);l=l||"default";var i=n.get(u);if(!i){var c={loading:0,preload:null};if(i=a.querySelector(Tn(u)))c.loading=5;else{t=N({rel:"stylesheet",href:t,"data-precedence":l},e),(e=Tl.get(u))&&nf(t,e);var o=i=a.createElement("link");Xt(o),Kt(o,"link",t),o._p=new Promise(function(b,z){o.onload=b,o.onerror=z}),o.addEventListener("load",function(){c.loading|=1}),o.addEventListener("error",function(){c.loading|=2}),c.loading|=4,wu(i,l,a)}i={type:"stylesheet",instance:i,count:1,state:c},n.set(u,i)}}}function g0(t,l){$l.X(t,l);var e=Oa;if(e&&t){var a=$e(e).hoistableScripts,n=Na(t),u=a.get(n);u||(u=e.querySelector(zn(n)),u||(t=N({src:t,async:!0},l),(l=Tl.get(n))&&uf(t,l),u=e.createElement("script"),Xt(u),Kt(u,"link",t),e.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function b0(t,l){$l.M(t,l);var e=Oa;if(e&&t){var a=$e(e).hoistableScripts,n=Na(t),u=a.get(n);u||(u=e.querySelector(zn(n)),u||(t=N({src:t,async:!0,type:"module"},l),(l=Tl.get(n))&&uf(t,l),u=e.createElement("script"),Xt(u),Kt(u,"link",t),e.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function cd(t,l,e,a){var n=(n=W.current)?Bu(n):null;if(!n)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof e.precedence=="string"&&typeof e.href=="string"?(l=Da(e.href),e=$e(n).hoistableStyles,a=e.get(l),a||(a={type:"style",instance:null,count:0,state:null},e.set(l,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(e.rel==="stylesheet"&&typeof e.href=="string"&&typeof e.precedence=="string"){t=Da(e.href);var u=$e(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Tn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),Tl.has(t)||(e={rel:"preload",as:"style",href:e.href,crossOrigin:e.crossOrigin,integrity:e.integrity,media:e.media,hrefLang:e.hrefLang,referrerPolicy:e.referrerPolicy},Tl.set(t,e),u||p0(n,t,e,i.state))),l&&a===null)throw Error(s(528,""));return i}if(l&&a!==null)throw Error(s(529,""));return null;case"script":return l=e.async,e=e.src,typeof e=="string"&&l&&typeof l!="function"&&typeof l!="symbol"?(l=Na(e),e=$e(n).hoistableScripts,a=e.get(l),a||(a={type:"script",instance:null,count:0,state:null},e.set(l,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Da(t){return'href="'+gl(t)+'"'}function Tn(t){return'link[rel="stylesheet"]['+t+"]"}function fd(t){return N({},t,{"data-precedence":t.precedence,precedence:null})}function p0(t,l,e,a){t.querySelector('link[rel="preload"][as="style"]['+l+"]")?a.loading=1:(l=t.createElement("link"),a.preload=l,l.addEventListener("load",function(){return a.loading|=1}),l.addEventListener("error",function(){return a.loading|=2}),Kt(l,"link",e),Xt(l),t.head.appendChild(l))}function Na(t){return'[src="'+gl(t)+'"]'}function zn(t){return"script[async]"+t}function sd(t,l,e){if(l.count++,l.instance===null)switch(l.type){case"style":var a=t.querySelector('style[data-href~="'+gl(e.href)+'"]');if(a)return l.instance=a,Xt(a),a;var n=N({},e,{"data-href":e.href,"data-precedence":e.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Xt(a),Kt(a,"style",n),wu(a,e.precedence,t),l.instance=a;case"stylesheet":n=Da(e.href);var u=t.querySelector(Tn(n));if(u)return l.state.loading|=4,l.instance=u,Xt(u),u;a=fd(e),(n=Tl.get(n))&&nf(a,n),u=(t.ownerDocument||t).createElement("link"),Xt(u);var i=u;return i._p=new Promise(function(c,o){i.onload=c,i.onerror=o}),Kt(u,"link",a),l.state.loading|=4,wu(u,e.precedence,t),l.instance=u;case"script":return u=Na(e.src),(n=t.querySelector(zn(u)))?(l.instance=n,Xt(n),n):(a=e,(n=Tl.get(u))&&(a=N({},e),uf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Xt(n),Kt(n,"link",a),t.head.appendChild(n),l.instance=n);case"void":return null;default:throw Error(s(443,l.type))}else l.type==="stylesheet"&&(l.state.loading&4)===0&&(a=l.instance,l.state.loading|=4,wu(a,e.precedence,t));return l.instance}function wu(t,l,e){for(var a=e.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var c=a[i];if(c.dataset.precedence===l)u=c;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(l=e.nodeType===9?e.head:e,l.insertBefore(t,l.firstChild))}function nf(t,l){t.crossOrigin==null&&(t.crossOrigin=l.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=l.referrerPolicy),t.title==null&&(t.title=l.title)}function uf(t,l){t.crossOrigin==null&&(t.crossOrigin=l.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=l.referrerPolicy),t.integrity==null&&(t.integrity=l.integrity)}var Yu=null;function od(t,l,e){if(Yu===null){var a=new Map,n=Yu=new Map;n.set(e,a)}else n=Yu,a=n.get(e),a||(a=new Map,n.set(e,a));if(a.has(t))return a;for(a.set(t,null),e=e.getElementsByTagName(t),n=0;n<e.length;n++){var u=e[n];if(!(u[Ba]||u[Jt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(l)||"";i=t+i;var c=a.get(i);c?c.push(u):a.set(i,[u])}}return a}function rd(t,l,e){t=t.ownerDocument||t,t.head.insertBefore(e,l==="title"?t.querySelector("head > title"):null)}function S0(t,l,e){if(e===1||l.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof l.precedence!="string"||typeof l.href!="string"||l.href==="")break;return!0;case"link":if(typeof l.rel!="string"||typeof l.href!="string"||l.href===""||l.onLoad||l.onError)break;switch(l.rel){case"stylesheet":return t=l.disabled,typeof l.precedence=="string"&&t==null;default:return!0}case"script":if(l.async&&typeof l.async!="function"&&typeof l.async!="symbol"&&!l.onLoad&&!l.onError&&l.src&&typeof l.src=="string")return!0}return!1}function dd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Mn=null;function x0(){}function A0(t,l,e){if(Mn===null)throw Error(s(475));var a=Mn;if(l.type==="stylesheet"&&(typeof e.media!="string"||matchMedia(e.media).matches!==!1)&&(l.state.loading&4)===0){if(l.instance===null){var n=Da(e.href),u=t.querySelector(Tn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Gu.bind(a),t.then(a,a)),l.state.loading|=4,l.instance=u,Xt(u);return}u=t.ownerDocument||t,e=fd(e),(n=Tl.get(n))&&nf(e,n),u=u.createElement("link"),Xt(u);var i=u;i._p=new Promise(function(c,o){i.onload=c,i.onerror=o}),Kt(u,"link",e),l.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(l,t),(t=l.state.preload)&&(l.state.loading&3)===0&&(a.count++,l=Gu.bind(a),t.addEventListener("load",l),t.addEventListener("error",l))}}function E0(){if(Mn===null)throw Error(s(475));var t=Mn;return t.stylesheets&&t.count===0&&cf(t,t.stylesheets),0<t.count?function(l){var e=setTimeout(function(){if(t.stylesheets&&cf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=l,function(){t.unsuspend=null,clearTimeout(e)}}:null}function Gu(){if(this.count--,this.count===0){if(this.stylesheets)cf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Xu=null;function cf(t,l){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Xu=new Map,l.forEach(T0,t),Xu=null,Gu.call(t))}function T0(t,l){if(!(l.state.loading&4)){var e=Xu.get(t);if(e)var a=e.get(null);else{e=new Map,Xu.set(t,e);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(e.set(i.dataset.precedence,i),a=i)}a&&e.set(null,a)}n=l.instance,i=n.getAttribute("data-precedence"),u=e.get(i)||a,u===a&&e.set(null,n),e.set(i,n),this.count++,a=Gu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),l.state.loading|=4}}var _n={$$typeof:et,Provider:null,Consumer:null,_currentValue:R,_currentValue2:R,_threadCount:0};function z0(t,l,e,a,n,u,i,c){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ei(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ei(0),this.hiddenUpdates=ei(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=c,this.incompleteTransitions=new Map}function md(t,l,e,a,n,u,i,c,o,b,z,O){return t=new z0(t,l,e,i,c,o,b,O),l=1,u===!0&&(l|=24),u=ol(3,null,null,l),t.current=u,u.stateNode=t,l=Xi(),l.refCount++,t.pooledCache=l,l.refCount++,u.memoizedState={element:a,isDehydrated:e,cache:l},Vi(u),t}function hd(t){return t?(t=ca,t):ca}function vd(t,l,e,a,n,u){n=hd(n),a.context===null?a.context=n:a.pendingContext=n,a=ie(l),a.payload={element:e},u=u===void 0?null:u,u!==null&&(a.callback=u),e=ce(t,a,l),e!==null&&(vl(e,t,l),en(e,t,l))}function yd(t,l){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var e=t.retryLane;t.retryLane=e!==0&&e<l?e:l}}function ff(t,l){yd(t,l),(t=t.alternate)&&yd(t,l)}function gd(t){if(t.tag===13){var l=ia(t,67108864);l!==null&&vl(l,t,67108864),ff(t,67108864)}}var Qu=!0;function M0(t,l,e,a){var n=T.T;T.T=null;var u=H.p;try{H.p=2,sf(t,l,e,a)}finally{H.p=u,T.T=n}}function _0(t,l,e,a){var n=T.T;T.T=null;var u=H.p;try{H.p=8,sf(t,l,e,a)}finally{H.p=u,T.T=n}}function sf(t,l,e,a){if(Qu){var n=of(a);if(n===null)$c(t,l,a,ku,e),pd(t,a);else if(D0(n,t,l,e,a))a.stopPropagation();else if(pd(t,a),l&4&&-1<O0.indexOf(t)){for(;n!==null;){var u=Je(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Me(u.pendingLanes);if(i!==0){var c=u;for(c.pendingLanes|=2,c.entangledLanes|=2;i;){var o=1<<31-fl(i);c.entanglements[1]|=o,i&=~o}Hl(u),(yt&6)===0&&(Mu=Dl()+500,Sn(0))}}break;case 13:c=ia(u,2),c!==null&&vl(c,u,2),Ou(),ff(u,2)}if(u=of(a),u===null&&$c(t,l,a,ku,e),u===n)break;n=u}n!==null&&a.stopPropagation()}else $c(t,l,a,null,e)}}function of(t){return t=hi(t),rf(t)}var ku=null;function rf(t){if(ku=null,t=Ke(t),t!==null){var l=E(t);if(l===null)t=null;else{var e=l.tag;if(e===13){if(t=M(l),t!==null)return t;t=null}else if(e===3){if(l.stateNode.current.memoizedState.isDehydrated)return l.tag===3?l.stateNode.containerInfo:null;t=null}else l!==t&&(t=null)}}return ku=t,null}function bd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(mm()){case Rf:return 2;case Uf:return 8;case Cn:case hm:return 32;case jf:return 268435456;default:return 32}default:return 32}}var df=!1,Se=null,xe=null,Ae=null,On=new Map,Dn=new Map,Ee=[],O0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function pd(t,l){switch(t){case"focusin":case"focusout":Se=null;break;case"dragenter":case"dragleave":xe=null;break;case"mouseover":case"mouseout":Ae=null;break;case"pointerover":case"pointerout":On.delete(l.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dn.delete(l.pointerId)}}function Nn(t,l,e,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:l,domEventName:e,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},l!==null&&(l=Je(l),l!==null&&gd(l)),t):(t.eventSystemFlags|=a,l=t.targetContainers,n!==null&&l.indexOf(n)===-1&&l.push(n),t)}function D0(t,l,e,a,n){switch(l){case"focusin":return Se=Nn(Se,t,l,e,a,n),!0;case"dragenter":return xe=Nn(xe,t,l,e,a,n),!0;case"mouseover":return Ae=Nn(Ae,t,l,e,a,n),!0;case"pointerover":var u=n.pointerId;return On.set(u,Nn(On.get(u)||null,t,l,e,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Dn.set(u,Nn(Dn.get(u)||null,t,l,e,a,n)),!0}return!1}function Sd(t){var l=Ke(t.target);if(l!==null){var e=E(l);if(e!==null){if(l=e.tag,l===13){if(l=M(e),l!==null){t.blockedOn=l,Am(t.priority,function(){if(e.tag===13){var a=hl();a=ai(a);var n=ia(e,a);n!==null&&vl(n,e,a),ff(e,a)}});return}}else if(l===3&&e.stateNode.current.memoizedState.isDehydrated){t.blockedOn=e.tag===3?e.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Zu(t){if(t.blockedOn!==null)return!1;for(var l=t.targetContainers;0<l.length;){var e=of(t.nativeEvent);if(e===null){e=t.nativeEvent;var a=new e.constructor(e.type,e);mi=a,e.target.dispatchEvent(a),mi=null}else return l=Je(e),l!==null&&gd(l),t.blockedOn=e,!1;l.shift()}return!0}function xd(t,l,e){Zu(t)&&e.delete(l)}function N0(){df=!1,Se!==null&&Zu(Se)&&(Se=null),xe!==null&&Zu(xe)&&(xe=null),Ae!==null&&Zu(Ae)&&(Ae=null),On.forEach(xd),Dn.forEach(xd)}function Vu(t,l){t.blockedOn===l&&(t.blockedOn=null,df||(df=!0,f.unstable_scheduleCallback(f.unstable_NormalPriority,N0)))}var Lu=null;function Ad(t){Lu!==t&&(Lu=t,f.unstable_scheduleCallback(f.unstable_NormalPriority,function(){Lu===t&&(Lu=null);for(var l=0;l<t.length;l+=3){var e=t[l],a=t[l+1],n=t[l+2];if(typeof a!="function"){if(rf(a||e)===null)continue;break}var u=Je(e);u!==null&&(t.splice(l,3),l-=3,oc(u,{pending:!0,data:n,method:e.method,action:a},a,n))}}))}function Rn(t){function l(o){return Vu(o,t)}Se!==null&&Vu(Se,t),xe!==null&&Vu(xe,t),Ae!==null&&Vu(Ae,t),On.forEach(l),Dn.forEach(l);for(var e=0;e<Ee.length;e++){var a=Ee[e];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Ee.length&&(e=Ee[0],e.blockedOn===null);)Sd(e),e.blockedOn===null&&Ee.shift();if(e=(t.ownerDocument||t).$$reactFormReplay,e!=null)for(a=0;a<e.length;a+=3){var n=e[a],u=e[a+1],i=n[tl]||null;if(typeof u=="function")i||Ad(e);else if(i){var c=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[tl]||null)c=i.formAction;else if(rf(n)!==null)continue}else c=i.action;typeof c=="function"?e[a+1]=c:(e.splice(a,3),a-=3),Ad(e)}}}function mf(t){this._internalRoot=t}Ku.prototype.render=mf.prototype.render=function(t){var l=this._internalRoot;if(l===null)throw Error(s(409));var e=l.current,a=hl();vd(e,a,t,l,null,null)},Ku.prototype.unmount=mf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var l=t.containerInfo;vd(t.current,2,null,t,null,null),Ou(),l[Le]=null}};function Ku(t){this._internalRoot=t}Ku.prototype.unstable_scheduleHydration=function(t){if(t){var l=wf();t={blockedOn:null,target:t,priority:l};for(var e=0;e<Ee.length&&l!==0&&l<Ee[e].priority;e++);Ee.splice(e,0,t),e===0&&Sd(t)}};var Ed=h.version;if(Ed!=="19.1.0")throw Error(s(527,Ed,"19.1.0"));H.findDOMNode=function(t){var l=t._reactInternals;if(l===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=A(l),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var R0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:T,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ju=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ju.isDisabled&&Ju.supportsFiber)try{Ha=Ju.inject(R0),cl=Ju}catch{}}return jn.createRoot=function(t,l){if(!g(t))throw Error(s(299));var e=!1,a="",n=Yo,u=Go,i=Xo,c=null;return l!=null&&(l.unstable_strictMode===!0&&(e=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(n=l.onUncaughtError),l.onCaughtError!==void 0&&(u=l.onCaughtError),l.onRecoverableError!==void 0&&(i=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(c=l.unstable_transitionCallbacks)),l=md(t,1,!1,null,null,e,a,n,u,i,c,null),t[Le]=l.current,Jc(t),new mf(l)},jn.hydrateRoot=function(t,l,e){if(!g(t))throw Error(s(299));var a=!1,n="",u=Yo,i=Go,c=Xo,o=null,b=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(u=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(c=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(o=e.unstable_transitionCallbacks),e.formState!==void 0&&(b=e.formState)),l=md(t,1,!0,l,e??null,a,n,u,i,c,o,b),l.context=hd(null),e=l.current,a=hl(),a=ai(a),n=ie(a),n.callback=null,ce(e,n,a),e=a,l.current.lanes=e,qa(l,e),Hl(l),t[Le]=l.current,Jc(t),new Ku(l)},jn.version="19.1.0",jn}var jd;function Z0(){if(jd)return yf.exports;jd=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(h){console.error(h)}}return f(),yf.exports=k0(),yf.exports}var V0=Z0();function Hd(f,h){if(typeof f=="function")return f(h);f!=null&&(f.current=h)}function L0(...f){return h=>{let m=!1;const s=f.map(g=>{const E=Hd(g,h);return!m&&typeof E=="function"&&(m=!0),E});if(m)return()=>{for(let g=0;g<s.length;g++){const E=s[g];typeof E=="function"?E():Hd(f[g],null)}}}}function Of(...f){return V.useCallback(L0(...f),f)}function Zd(f){const h=J0(f),m=V.forwardRef((s,g)=>{const{children:E,...M}=s,U=V.Children.toArray(E),A=U.find(W0);if(A){const p=A.props.children,N=U.map(X=>X===A?V.Children.count(p)>1?V.Children.only(null):V.isValidElement(p)?p.props.children:null:X);return C.jsx(h,{...M,ref:g,children:V.isValidElement(p)?V.cloneElement(p,void 0,N):null})}return C.jsx(h,{...M,ref:g,children:E})});return m.displayName=`${f}.Slot`,m}var K0=Zd("Slot");function J0(f){const h=V.forwardRef((m,s)=>{const{children:g,...E}=m,M=V.isValidElement(g)?P0(g):void 0,U=Of(M,s);if(V.isValidElement(g)){const A=F0(E,g.props);return g.type!==V.Fragment&&(A.ref=U),V.cloneElement(g,A)}return V.Children.count(g)>1?V.Children.only(null):null});return h.displayName=`${f}.SlotClone`,h}var $0=Symbol("radix.slottable");function W0(f){return V.isValidElement(f)&&typeof f.type=="function"&&"__radixId"in f.type&&f.type.__radixId===$0}function F0(f,h){const m={...h};for(const s in h){const g=f[s],E=h[s];/^on[A-Z]/.test(s)?g&&E?m[s]=(...U)=>{const A=E(...U);return g(...U),A}:g&&(m[s]=g):s==="style"?m[s]={...g,...E}:s==="className"&&(m[s]=[g,E].filter(Boolean).join(" "))}return{...f,...m}}function P0(f){var s,g;let h=(s=Object.getOwnPropertyDescriptor(f.props,"ref"))==null?void 0:s.get,m=h&&"isReactWarning"in h&&h.isReactWarning;return m?f.ref:(h=(g=Object.getOwnPropertyDescriptor(f,"ref"))==null?void 0:g.get,m=h&&"isReactWarning"in h&&h.isReactWarning,m?f.props.ref:f.props.ref||f.ref)}function Vd(f){var h,m,s="";if(typeof f=="string"||typeof f=="number")s+=f;else if(typeof f=="object")if(Array.isArray(f)){var g=f.length;for(h=0;h<g;h++)f[h]&&(m=Vd(f[h]))&&(s&&(s+=" "),s+=m)}else for(m in f)f[m]&&(s&&(s+=" "),s+=m);return s}function Ld(){for(var f,h,m=0,s="",g=arguments.length;m<g;m++)(f=arguments[m])&&(h=Vd(f))&&(s&&(s+=" "),s+=h);return s}const Cd=f=>typeof f=="boolean"?`${f}`:f===0?"0":f,qd=Ld,I0=(f,h)=>m=>{var s;if((h==null?void 0:h.variants)==null)return qd(f,m==null?void 0:m.class,m==null?void 0:m.className);const{variants:g,defaultVariants:E}=h,M=Object.keys(g).map(p=>{const N=m==null?void 0:m[p],X=E==null?void 0:E[p];if(N===null)return null;const Q=Cd(N)||Cd(X);return g[p][Q]}),U=m&&Object.entries(m).reduce((p,N)=>{let[X,Q]=N;return Q===void 0||(p[X]=Q),p},{}),A=h==null||(s=h.compoundVariants)===null||s===void 0?void 0:s.reduce((p,N)=>{let{class:X,className:Q,...ft}=N;return Object.entries(ft).every(P=>{let[nt,ot]=P;return Array.isArray(ot)?ot.includes({...E,...U}[nt]):{...E,...U}[nt]===ot})?[...p,X,Q]:p},[]);return qd(f,M,A,m==null?void 0:m.class,m==null?void 0:m.className)},Df="-",tv=f=>{const h=ev(f),{conflictingClassGroups:m,conflictingClassGroupModifiers:s}=f;return{getClassGroupId:M=>{const U=M.split(Df);return U[0]===""&&U.length!==1&&U.shift(),Kd(U,h)||lv(M)},getConflictingClassGroupIds:(M,U)=>{const A=m[M]||[];return U&&s[M]?[...A,...s[M]]:A}}},Kd=(f,h)=>{var M;if(f.length===0)return h.classGroupId;const m=f[0],s=h.nextPart.get(m),g=s?Kd(f.slice(1),s):void 0;if(g)return g;if(h.validators.length===0)return;const E=f.join(Df);return(M=h.validators.find(({validator:U})=>U(E)))==null?void 0:M.classGroupId},Bd=/^\[(.+)\]$/,lv=f=>{if(Bd.test(f)){const h=Bd.exec(f)[1],m=h==null?void 0:h.substring(0,h.indexOf(":"));if(m)return"arbitrary.."+m}},ev=f=>{const{theme:h,classGroups:m}=f,s={nextPart:new Map,validators:[]};for(const g in m)Tf(m[g],s,g,h);return s},Tf=(f,h,m,s)=>{f.forEach(g=>{if(typeof g=="string"){const E=g===""?h:wd(h,g);E.classGroupId=m;return}if(typeof g=="function"){if(av(g)){Tf(g(s),h,m,s);return}h.validators.push({validator:g,classGroupId:m});return}Object.entries(g).forEach(([E,M])=>{Tf(M,wd(h,E),m,s)})})},wd=(f,h)=>{let m=f;return h.split(Df).forEach(s=>{m.nextPart.has(s)||m.nextPart.set(s,{nextPart:new Map,validators:[]}),m=m.nextPart.get(s)}),m},av=f=>f.isThemeGetter,nv=f=>{if(f<1)return{get:()=>{},set:()=>{}};let h=0,m=new Map,s=new Map;const g=(E,M)=>{m.set(E,M),h++,h>f&&(h=0,s=m,m=new Map)};return{get(E){let M=m.get(E);if(M!==void 0)return M;if((M=s.get(E))!==void 0)return g(E,M),M},set(E,M){m.has(E)?m.set(E,M):g(E,M)}}},zf="!",Mf=":",uv=Mf.length,iv=f=>{const{prefix:h,experimentalParseClassName:m}=f;let s=g=>{const E=[];let M=0,U=0,A=0,p;for(let P=0;P<g.length;P++){let nt=g[P];if(M===0&&U===0){if(nt===Mf){E.push(g.slice(A,P)),A=P+uv;continue}if(nt==="/"){p=P;continue}}nt==="["?M++:nt==="]"?M--:nt==="("?U++:nt===")"&&U--}const N=E.length===0?g:g.substring(A),X=cv(N),Q=X!==N,ft=p&&p>A?p-A:void 0;return{modifiers:E,hasImportantModifier:Q,baseClassName:X,maybePostfixModifierPosition:ft}};if(h){const g=h+Mf,E=s;s=M=>M.startsWith(g)?E(M.substring(g.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:M,maybePostfixModifierPosition:void 0}}if(m){const g=s;s=E=>m({className:E,parseClassName:g})}return s},cv=f=>f.endsWith(zf)?f.substring(0,f.length-1):f.startsWith(zf)?f.substring(1):f,fv=f=>{const h=Object.fromEntries(f.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const g=[];let E=[];return s.forEach(M=>{M[0]==="["||h[M]?(g.push(...E.sort(),M),E=[]):E.push(M)}),g.push(...E.sort()),g}},sv=f=>({cache:nv(f.cacheSize),parseClassName:iv(f),sortModifiers:fv(f),...tv(f)}),ov=/\s+/,rv=(f,h)=>{const{parseClassName:m,getClassGroupId:s,getConflictingClassGroupIds:g,sortModifiers:E}=h,M=[],U=f.trim().split(ov);let A="";for(let p=U.length-1;p>=0;p-=1){const N=U[p],{isExternal:X,modifiers:Q,hasImportantModifier:ft,baseClassName:P,maybePostfixModifierPosition:nt}=m(N);if(X){A=N+(A.length>0?" "+A:A);continue}let ot=!!nt,L=s(ot?P.substring(0,nt):P);if(!L){if(!ot){A=N+(A.length>0?" "+A:A);continue}if(L=s(P),!L){A=N+(A.length>0?" "+A:A);continue}ot=!1}const rt=E(Q).join(":"),et=ft?rt+zf:rt,_t=et+L;if(M.includes(_t))continue;M.push(_t);const F=g(L,ot);for(let Et=0;Et<F.length;++Et){const Rt=F[Et];M.push(et+Rt)}A=N+(A.length>0?" "+A:A)}return A};function dv(){let f=0,h,m,s="";for(;f<arguments.length;)(h=arguments[f++])&&(m=Jd(h))&&(s&&(s+=" "),s+=m);return s}const Jd=f=>{if(typeof f=="string")return f;let h,m="";for(let s=0;s<f.length;s++)f[s]&&(h=Jd(f[s]))&&(m&&(m+=" "),m+=h);return m};function mv(f,...h){let m,s,g,E=M;function M(A){const p=h.reduce((N,X)=>X(N),f());return m=sv(p),s=m.cache.get,g=m.cache.set,E=U,U(A)}function U(A){const p=s(A);if(p)return p;const N=rv(A,m);return g(A,N),N}return function(){return E(dv.apply(null,arguments))}}const Gt=f=>{const h=m=>m[f]||[];return h.isThemeGetter=!0,h},$d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Wd=/^\((?:(\w[\w-]*):)?(.+)\)$/i,hv=/^\d+\/\d+$/,vv=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,yv=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,gv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,bv=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,pv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ra=f=>hv.test(f),lt=f=>!!f&&!Number.isNaN(Number(f)),ze=f=>!!f&&Number.isInteger(Number(f)),Sf=f=>f.endsWith("%")&&lt(f.slice(0,-1)),Wl=f=>vv.test(f),Sv=()=>!0,xv=f=>yv.test(f)&&!gv.test(f),Fd=()=>!1,Av=f=>bv.test(f),Ev=f=>pv.test(f),Tv=f=>!Y(f)&&!G(f),zv=f=>Ua(f,tm,Fd),Y=f=>$d.test(f),Ze=f=>Ua(f,lm,xv),xf=f=>Ua(f,Nv,lt),Yd=f=>Ua(f,Pd,Fd),Mv=f=>Ua(f,Id,Ev),$u=f=>Ua(f,em,Av),G=f=>Wd.test(f),Hn=f=>ja(f,lm),_v=f=>ja(f,Rv),Gd=f=>ja(f,Pd),Ov=f=>ja(f,tm),Dv=f=>ja(f,Id),Wu=f=>ja(f,em,!0),Ua=(f,h,m)=>{const s=$d.exec(f);return s?s[1]?h(s[1]):m(s[2]):!1},ja=(f,h,m=!1)=>{const s=Wd.exec(f);return s?s[1]?h(s[1]):m:!1},Pd=f=>f==="position"||f==="percentage",Id=f=>f==="image"||f==="url",tm=f=>f==="length"||f==="size"||f==="bg-size",lm=f=>f==="length",Nv=f=>f==="number",Rv=f=>f==="family-name",em=f=>f==="shadow",Uv=()=>{const f=Gt("color"),h=Gt("font"),m=Gt("text"),s=Gt("font-weight"),g=Gt("tracking"),E=Gt("leading"),M=Gt("breakpoint"),U=Gt("container"),A=Gt("spacing"),p=Gt("radius"),N=Gt("shadow"),X=Gt("inset-shadow"),Q=Gt("text-shadow"),ft=Gt("drop-shadow"),P=Gt("blur"),nt=Gt("perspective"),ot=Gt("aspect"),L=Gt("ease"),rt=Gt("animate"),et=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_t=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],F=()=>[..._t(),G,Y],Et=()=>["auto","hidden","clip","visible","scroll"],Rt=()=>["auto","contain","none"],k=()=>[G,Y,A],Mt=()=>[Ra,"full","auto",...k()],zl=()=>[ze,"none","subgrid",G,Y],Pt=()=>["auto",{span:["full",ze,G,Y]},ze,G,Y],Ot=()=>[ze,"auto",G,Y],ul=()=>["auto","min","max","fr",G,Y],It=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Tt=()=>["start","end","center","stretch","center-safe","end-safe"],T=()=>["auto",...k()],H=()=>[Ra,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],R=()=>[f,G,Y],vt=()=>[..._t(),Gd,Yd,{position:[G,Y]}],r=()=>["no-repeat",{repeat:["","x","y","space","round"]}],D=()=>["auto","cover","contain",Ov,zv,{size:[G,Y]}],q=()=>[Sf,Hn,Ze],j=()=>["","none","full",p,G,Y],B=()=>["",lt,Hn,Ze],ut=()=>["solid","dashed","dotted","double"],W=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],dt=()=>[lt,Sf,Gd,Yd],xt=()=>["","none",P,G,Y],il=()=>["none",lt,G,Y],Pl=()=>["none",lt,G,Y],Il=()=>[lt,G,Y],te=()=>[Ra,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Wl],breakpoint:[Wl],color:[Sv],container:[Wl],"drop-shadow":[Wl],ease:["in","out","in-out"],font:[Tv],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Wl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Wl],shadow:[Wl],spacing:["px",lt],text:[Wl],"text-shadow":[Wl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ra,Y,G,ot]}],container:["container"],columns:[{columns:[lt,Y,G,U]}],"break-after":[{"break-after":et()}],"break-before":[{"break-before":et()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:F()}],overflow:[{overflow:Et()}],"overflow-x":[{"overflow-x":Et()}],"overflow-y":[{"overflow-y":Et()}],overscroll:[{overscroll:Rt()}],"overscroll-x":[{"overscroll-x":Rt()}],"overscroll-y":[{"overscroll-y":Rt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Mt()}],"inset-x":[{"inset-x":Mt()}],"inset-y":[{"inset-y":Mt()}],start:[{start:Mt()}],end:[{end:Mt()}],top:[{top:Mt()}],right:[{right:Mt()}],bottom:[{bottom:Mt()}],left:[{left:Mt()}],visibility:["visible","invisible","collapse"],z:[{z:[ze,"auto",G,Y]}],basis:[{basis:[Ra,"full","auto",U,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[lt,Ra,"auto","initial","none",Y]}],grow:[{grow:["",lt,G,Y]}],shrink:[{shrink:["",lt,G,Y]}],order:[{order:[ze,"first","last","none",G,Y]}],"grid-cols":[{"grid-cols":zl()}],"col-start-end":[{col:Pt()}],"col-start":[{"col-start":Ot()}],"col-end":[{"col-end":Ot()}],"grid-rows":[{"grid-rows":zl()}],"row-start-end":[{row:Pt()}],"row-start":[{"row-start":Ot()}],"row-end":[{"row-end":Ot()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ul()}],"auto-rows":[{"auto-rows":ul()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...It(),"normal"]}],"justify-items":[{"justify-items":[...Tt(),"normal"]}],"justify-self":[{"justify-self":["auto",...Tt()]}],"align-content":[{content:["normal",...It()]}],"align-items":[{items:[...Tt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Tt(),{baseline:["","last"]}]}],"place-content":[{"place-content":It()}],"place-items":[{"place-items":[...Tt(),"baseline"]}],"place-self":[{"place-self":["auto",...Tt()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[U,"screen",...H()]}],"min-w":[{"min-w":[U,"screen","none",...H()]}],"max-w":[{"max-w":[U,"screen","none","prose",{screen:[M]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",m,Hn,Ze]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,G,xf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Sf,Y]}],"font-family":[{font:[_v,Y,h]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[g,G,Y]}],"line-clamp":[{"line-clamp":[lt,"none",G,xf]}],leading:[{leading:[E,...k()]}],"list-image":[{"list-image":["none",G,Y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,Y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:R()}],"text-color":[{text:R()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ut(),"wavy"]}],"text-decoration-thickness":[{decoration:[lt,"from-font","auto",G,Ze]}],"text-decoration-color":[{decoration:R()}],"underline-offset":[{"underline-offset":[lt,"auto",G,Y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,Y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,Y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:vt()}],"bg-repeat":[{bg:r()}],"bg-size":[{bg:D()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ze,G,Y],radial:["",G,Y],conic:[ze,G,Y]},Dv,Mv]}],"bg-color":[{bg:R()}],"gradient-from-pos":[{from:q()}],"gradient-via-pos":[{via:q()}],"gradient-to-pos":[{to:q()}],"gradient-from":[{from:R()}],"gradient-via":[{via:R()}],"gradient-to":[{to:R()}],rounded:[{rounded:j()}],"rounded-s":[{"rounded-s":j()}],"rounded-e":[{"rounded-e":j()}],"rounded-t":[{"rounded-t":j()}],"rounded-r":[{"rounded-r":j()}],"rounded-b":[{"rounded-b":j()}],"rounded-l":[{"rounded-l":j()}],"rounded-ss":[{"rounded-ss":j()}],"rounded-se":[{"rounded-se":j()}],"rounded-ee":[{"rounded-ee":j()}],"rounded-es":[{"rounded-es":j()}],"rounded-tl":[{"rounded-tl":j()}],"rounded-tr":[{"rounded-tr":j()}],"rounded-br":[{"rounded-br":j()}],"rounded-bl":[{"rounded-bl":j()}],"border-w":[{border:B()}],"border-w-x":[{"border-x":B()}],"border-w-y":[{"border-y":B()}],"border-w-s":[{"border-s":B()}],"border-w-e":[{"border-e":B()}],"border-w-t":[{"border-t":B()}],"border-w-r":[{"border-r":B()}],"border-w-b":[{"border-b":B()}],"border-w-l":[{"border-l":B()}],"divide-x":[{"divide-x":B()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":B()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ut(),"hidden","none"]}],"divide-style":[{divide:[...ut(),"hidden","none"]}],"border-color":[{border:R()}],"border-color-x":[{"border-x":R()}],"border-color-y":[{"border-y":R()}],"border-color-s":[{"border-s":R()}],"border-color-e":[{"border-e":R()}],"border-color-t":[{"border-t":R()}],"border-color-r":[{"border-r":R()}],"border-color-b":[{"border-b":R()}],"border-color-l":[{"border-l":R()}],"divide-color":[{divide:R()}],"outline-style":[{outline:[...ut(),"none","hidden"]}],"outline-offset":[{"outline-offset":[lt,G,Y]}],"outline-w":[{outline:["",lt,Hn,Ze]}],"outline-color":[{outline:R()}],shadow:[{shadow:["","none",N,Wu,$u]}],"shadow-color":[{shadow:R()}],"inset-shadow":[{"inset-shadow":["none",X,Wu,$u]}],"inset-shadow-color":[{"inset-shadow":R()}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:R()}],"ring-offset-w":[{"ring-offset":[lt,Ze]}],"ring-offset-color":[{"ring-offset":R()}],"inset-ring-w":[{"inset-ring":B()}],"inset-ring-color":[{"inset-ring":R()}],"text-shadow":[{"text-shadow":["none",Q,Wu,$u]}],"text-shadow-color":[{"text-shadow":R()}],opacity:[{opacity:[lt,G,Y]}],"mix-blend":[{"mix-blend":[...W(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":W()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[lt]}],"mask-image-linear-from-pos":[{"mask-linear-from":dt()}],"mask-image-linear-to-pos":[{"mask-linear-to":dt()}],"mask-image-linear-from-color":[{"mask-linear-from":R()}],"mask-image-linear-to-color":[{"mask-linear-to":R()}],"mask-image-t-from-pos":[{"mask-t-from":dt()}],"mask-image-t-to-pos":[{"mask-t-to":dt()}],"mask-image-t-from-color":[{"mask-t-from":R()}],"mask-image-t-to-color":[{"mask-t-to":R()}],"mask-image-r-from-pos":[{"mask-r-from":dt()}],"mask-image-r-to-pos":[{"mask-r-to":dt()}],"mask-image-r-from-color":[{"mask-r-from":R()}],"mask-image-r-to-color":[{"mask-r-to":R()}],"mask-image-b-from-pos":[{"mask-b-from":dt()}],"mask-image-b-to-pos":[{"mask-b-to":dt()}],"mask-image-b-from-color":[{"mask-b-from":R()}],"mask-image-b-to-color":[{"mask-b-to":R()}],"mask-image-l-from-pos":[{"mask-l-from":dt()}],"mask-image-l-to-pos":[{"mask-l-to":dt()}],"mask-image-l-from-color":[{"mask-l-from":R()}],"mask-image-l-to-color":[{"mask-l-to":R()}],"mask-image-x-from-pos":[{"mask-x-from":dt()}],"mask-image-x-to-pos":[{"mask-x-to":dt()}],"mask-image-x-from-color":[{"mask-x-from":R()}],"mask-image-x-to-color":[{"mask-x-to":R()}],"mask-image-y-from-pos":[{"mask-y-from":dt()}],"mask-image-y-to-pos":[{"mask-y-to":dt()}],"mask-image-y-from-color":[{"mask-y-from":R()}],"mask-image-y-to-color":[{"mask-y-to":R()}],"mask-image-radial":[{"mask-radial":[G,Y]}],"mask-image-radial-from-pos":[{"mask-radial-from":dt()}],"mask-image-radial-to-pos":[{"mask-radial-to":dt()}],"mask-image-radial-from-color":[{"mask-radial-from":R()}],"mask-image-radial-to-color":[{"mask-radial-to":R()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":_t()}],"mask-image-conic-pos":[{"mask-conic":[lt]}],"mask-image-conic-from-pos":[{"mask-conic-from":dt()}],"mask-image-conic-to-pos":[{"mask-conic-to":dt()}],"mask-image-conic-from-color":[{"mask-conic-from":R()}],"mask-image-conic-to-color":[{"mask-conic-to":R()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:vt()}],"mask-repeat":[{mask:r()}],"mask-size":[{mask:D()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,Y]}],filter:[{filter:["","none",G,Y]}],blur:[{blur:xt()}],brightness:[{brightness:[lt,G,Y]}],contrast:[{contrast:[lt,G,Y]}],"drop-shadow":[{"drop-shadow":["","none",ft,Wu,$u]}],"drop-shadow-color":[{"drop-shadow":R()}],grayscale:[{grayscale:["",lt,G,Y]}],"hue-rotate":[{"hue-rotate":[lt,G,Y]}],invert:[{invert:["",lt,G,Y]}],saturate:[{saturate:[lt,G,Y]}],sepia:[{sepia:["",lt,G,Y]}],"backdrop-filter":[{"backdrop-filter":["","none",G,Y]}],"backdrop-blur":[{"backdrop-blur":xt()}],"backdrop-brightness":[{"backdrop-brightness":[lt,G,Y]}],"backdrop-contrast":[{"backdrop-contrast":[lt,G,Y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",lt,G,Y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[lt,G,Y]}],"backdrop-invert":[{"backdrop-invert":["",lt,G,Y]}],"backdrop-opacity":[{"backdrop-opacity":[lt,G,Y]}],"backdrop-saturate":[{"backdrop-saturate":[lt,G,Y]}],"backdrop-sepia":[{"backdrop-sepia":["",lt,G,Y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,Y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[lt,"initial",G,Y]}],ease:[{ease:["linear","initial",L,G,Y]}],delay:[{delay:[lt,G,Y]}],animate:[{animate:["none",rt,G,Y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[nt,G,Y]}],"perspective-origin":[{"perspective-origin":F()}],rotate:[{rotate:il()}],"rotate-x":[{"rotate-x":il()}],"rotate-y":[{"rotate-y":il()}],"rotate-z":[{"rotate-z":il()}],scale:[{scale:Pl()}],"scale-x":[{"scale-x":Pl()}],"scale-y":[{"scale-y":Pl()}],"scale-z":[{"scale-z":Pl()}],"scale-3d":["scale-3d"],skew:[{skew:Il()}],"skew-x":[{"skew-x":Il()}],"skew-y":[{"skew-y":Il()}],transform:[{transform:[G,Y,"","none","gpu","cpu"]}],"transform-origin":[{origin:F()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:te()}],"translate-x":[{"translate-x":te()}],"translate-y":[{"translate-y":te()}],"translate-z":[{"translate-z":te()}],"translate-none":["translate-none"],accent:[{accent:R()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:R()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,Y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,Y]}],fill:[{fill:["none",...R()]}],"stroke-w":[{stroke:[lt,Hn,Ze,xf]}],stroke:[{stroke:["none",...R()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},jv=mv(Uv);function Fl(...f){return jv(Ld(f))}const Hv=I0("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Cv({className:f,variant:h,size:m,asChild:s=!1,...g}){const E=s?K0:"button";return C.jsx(E,{"data-slot":"button",className:Fl(Hv({variant:h,size:m,className:f})),...g})}function Af({className:f,type:h,...m}){return C.jsx("input",{type:h,"data-slot":"input",className:Fl("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",f),...m})}kd();var qv=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Nf=qv.reduce((f,h)=>{const m=Zd(`Primitive.${h}`),s=V.forwardRef((g,E)=>{const{asChild:M,...U}=g,A=M?m:h;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C.jsx(A,{...U,ref:E})});return s.displayName=`Primitive.${h}`,{...f,[h]:s}},{}),Bv="Label",am=V.forwardRef((f,h)=>C.jsx(Nf.label,{...f,ref:h,onMouseDown:m=>{var g;m.target.closest("button, input, select, textarea")||((g=f.onMouseDown)==null||g.call(f,m),!m.defaultPrevented&&m.detail>1&&m.preventDefault())}}));am.displayName=Bv;var wv=am;function Fu({className:f,...h}){return C.jsx(wv,{"data-slot":"label",className:Fl("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",f),...h})}function Yv(f,h,{checkForDefaultPrevented:m=!0}={}){return function(g){if(f==null||f(g),m===!1||!g.defaultPrevented)return h==null?void 0:h(g)}}function Gv(f,h=[]){let m=[];function s(E,M){const U=V.createContext(M),A=m.length;m=[...m,M];const p=X=>{var L;const{scope:Q,children:ft,...P}=X,nt=((L=Q==null?void 0:Q[f])==null?void 0:L[A])||U,ot=V.useMemo(()=>P,Object.values(P));return C.jsx(nt.Provider,{value:ot,children:ft})};p.displayName=E+"Provider";function N(X,Q){var nt;const ft=((nt=Q==null?void 0:Q[f])==null?void 0:nt[A])||U,P=V.useContext(ft);if(P)return P;if(M!==void 0)return M;throw new Error(`\`${X}\` must be used within \`${E}\``)}return[p,N]}const g=()=>{const E=m.map(M=>V.createContext(M));return function(U){const A=(U==null?void 0:U[f])||E;return V.useMemo(()=>({[`__scope${f}`]:{...U,[f]:A}}),[U,A])}};return g.scopeName=f,[s,Xv(g,...h)]}function Xv(...f){const h=f[0];if(f.length===1)return h;const m=()=>{const s=f.map(g=>({useScope:g(),scopeName:g.scopeName}));return function(E){const M=s.reduce((U,{useScope:A,scopeName:p})=>{const X=A(E)[`__scope${p}`];return{...U,...X}},{});return V.useMemo(()=>({[`__scope${h.scopeName}`]:M}),[M])}};return m.scopeName=h.scopeName,m}var nm=globalThis!=null&&globalThis.document?V.useLayoutEffect:()=>{},Qv=Y0[" useInsertionEffect ".trim().toString()]||nm;function kv({prop:f,defaultProp:h,onChange:m=()=>{},caller:s}){const[g,E,M]=Zv({defaultProp:h,onChange:m}),U=f!==void 0,A=U?f:g;{const N=V.useRef(f!==void 0);V.useEffect(()=>{const X=N.current;X!==U&&console.warn(`${s} is changing from ${X?"controlled":"uncontrolled"} to ${U?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),N.current=U},[U,s])}const p=V.useCallback(N=>{var X;if(U){const Q=Vv(N)?N(f):N;Q!==f&&((X=M.current)==null||X.call(M,Q))}else E(N)},[U,f,E,M]);return[A,p]}function Zv({defaultProp:f,onChange:h}){const[m,s]=V.useState(f),g=V.useRef(m),E=V.useRef(h);return Qv(()=>{E.current=h},[h]),V.useEffect(()=>{var M;g.current!==m&&((M=E.current)==null||M.call(E,m),g.current=m)},[m,g]),[m,s,E]}function Vv(f){return typeof f=="function"}function Lv(f){const h=V.useRef({value:f,previous:f});return V.useMemo(()=>(h.current.value!==f&&(h.current.previous=h.current.value,h.current.value=f),h.current.previous),[f])}function Kv(f){const[h,m]=V.useState(void 0);return nm(()=>{if(f){m({width:f.offsetWidth,height:f.offsetHeight});const s=new ResizeObserver(g=>{if(!Array.isArray(g)||!g.length)return;const E=g[0];let M,U;if("borderBoxSize"in E){const A=E.borderBoxSize,p=Array.isArray(A)?A[0]:A;M=p.inlineSize,U=p.blockSize}else M=f.offsetWidth,U=f.offsetHeight;m({width:M,height:U})});return s.observe(f,{box:"border-box"}),()=>s.unobserve(f)}else m(void 0)},[f]),h}var Pu="Switch",[Jv,Ay]=Gv(Pu),[$v,Wv]=Jv(Pu),um=V.forwardRef((f,h)=>{const{__scopeSwitch:m,name:s,checked:g,defaultChecked:E,required:M,disabled:U,value:A="on",onCheckedChange:p,form:N,...X}=f,[Q,ft]=V.useState(null),P=Of(h,et=>ft(et)),nt=V.useRef(!1),ot=Q?N||!!Q.closest("form"):!0,[L,rt]=kv({prop:g,defaultProp:E??!1,onChange:p,caller:Pu});return C.jsxs($v,{scope:m,checked:L,disabled:U,children:[C.jsx(Nf.button,{type:"button",role:"switch","aria-checked":L,"aria-required":M,"data-state":sm(L),"data-disabled":U?"":void 0,disabled:U,value:A,...X,ref:P,onClick:Yv(f.onClick,et=>{rt(_t=>!_t),ot&&(nt.current=et.isPropagationStopped(),nt.current||et.stopPropagation())})}),ot&&C.jsx(fm,{control:Q,bubbles:!nt.current,name:s,value:A,checked:L,required:M,disabled:U,form:N,style:{transform:"translateX(-100%)"}})]})});um.displayName=Pu;var im="SwitchThumb",cm=V.forwardRef((f,h)=>{const{__scopeSwitch:m,...s}=f,g=Wv(im,m);return C.jsx(Nf.span,{"data-state":sm(g.checked),"data-disabled":g.disabled?"":void 0,...s,ref:h})});cm.displayName=im;var Fv="SwitchBubbleInput",fm=V.forwardRef(({__scopeSwitch:f,control:h,checked:m,bubbles:s=!0,...g},E)=>{const M=V.useRef(null),U=Of(M,E),A=Lv(m),p=Kv(h);return V.useEffect(()=>{const N=M.current;if(!N)return;const X=window.HTMLInputElement.prototype,ft=Object.getOwnPropertyDescriptor(X,"checked").set;if(A!==m&&ft){const P=new Event("click",{bubbles:s});ft.call(N,m),N.dispatchEvent(P)}},[A,m,s]),C.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:m,...g,tabIndex:-1,ref:U,style:{...g.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});fm.displayName=Fv;function sm(f){return f?"checked":"unchecked"}var Pv=um,Iv=cm;function ty({className:f,...h}){return C.jsx(Pv,{"data-slot":"switch",className:Fl("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",f),...h,children:C.jsx(Iv,{"data-slot":"switch-thumb",className:Fl("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}function ly({className:f,...h}){return C.jsx("div",{"data-slot":"card",className:Fl("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",f),...h})}function ey({className:f,...h}){return C.jsx("div",{"data-slot":"card-header",className:Fl("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",f),...h})}function ay({className:f,...h}){return C.jsx("div",{"data-slot":"card-title",className:Fl("leading-none font-semibold",f),...h})}function ny({className:f,...h}){return C.jsx("div",{"data-slot":"card-content",className:Fl("px-6",f),...h})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uy=f=>f.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),iy=f=>f.replace(/^([A-Z])|[\s-_]+(\w)/g,(h,m,s)=>s?s.toUpperCase():m.toLowerCase()),Xd=f=>{const h=iy(f);return h.charAt(0).toUpperCase()+h.slice(1)},om=(...f)=>f.filter((h,m,s)=>!!h&&h.trim()!==""&&s.indexOf(h)===m).join(" ").trim(),cy=f=>{for(const h in f)if(h.startsWith("aria-")||h==="role"||h==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sy=V.forwardRef(({color:f="currentColor",size:h=24,strokeWidth:m=2,absoluteStrokeWidth:s,className:g="",children:E,iconNode:M,...U},A)=>V.createElement("svg",{ref:A,...fy,width:h,height:h,stroke:f,strokeWidth:s?Number(m)*24/Number(h):m,className:om("lucide",g),...!E&&!cy(U)&&{"aria-hidden":"true"},...U},[...M.map(([p,N])=>V.createElement(p,N)),...Array.isArray(E)?E:[E]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iu=(f,h)=>{const m=V.forwardRef(({className:s,...g},E)=>V.createElement(sy,{ref:E,iconNode:h,className:om(`lucide-${uy(Xd(f))}`,`lucide-${f}`,s),...g}));return m.displayName=Xd(f),m};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oy=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],ry=Iu("moon",oy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dy=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],my=Iu("settings",dy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hy=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],vy=Iu("sun",hy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yy=[["path",{d:"M12 2v8",key:"1q4o3n"}],["path",{d:"m4.93 10.93 1.41 1.41",key:"2a7f42"}],["path",{d:"M2 18h2",key:"j10viu"}],["path",{d:"M20 18h2",key:"wocana"}],["path",{d:"m19.07 10.93-1.41 1.41",key:"15zs5n"}],["path",{d:"M22 22H2",key:"19qnx5"}],["path",{d:"m8 6 4-4 4 4",key:"ybng9g"}],["path",{d:"M16 18a4 4 0 0 0-8 0",key:"1lzouq"}]],gy=Iu("sunrise",yy),Qd="/assets/mosque-bg-CDSgHlcA.jpg",Ef=[{text:"Kushdo që duron dhe fal, ta dijë se, në të vërtetë, këto janë nga veprimet më të virtytshme.",source:"Shura - Ajeti 43"},{text:'Nuk ka ndodhur që të ketë ndonjë të pandëgjueshëm ndaj prindërve të tij, vetëmse e kemi gjetur të ishte arrogant, i palumtur. Pastaj e lexoi fjalën e Allahut të Lartësuar: "Më ka bërë të mirësjellshëm ndaj nënës sime, e nuk më ka bërë kryelartë dhe as të pa lumtur (shekija)"',source:"Merjem: 32"},{text:"E kush beson në Allah dhe bën vepra të mira, Ai do t'i fusë në kopshte nëpër të cilat rrjedhin lumenj, ku do të qëndrojnë përgjithmonë.",source:"Nisa: 57"},{text:"Dhe kush i frikësohet Allahut, Ai do t'i bëjë një rrugëdalje dhe do ta furnizojë nga aty ku nuk e pret.",source:"Talak: 2-3"},{text:"O ju që besoni! Kërkoni ndihmë me durim dhe me namaz. Vërtet, Allahu është me të durueshmit.",source:"Bekare: 153"},{text:"Dhe kush shpëton një jetë, është sikur të ketë shpëtuar gjithë njerëzimin.",source:"Maide: 32"},{text:"Allahu nuk e ngarkon asnjë shpirt përtej mundësive të tij.",source:"Bekare: 286"},{text:"Dhe kush mbështetet tek Allahu, atij Ai i mjafton. Vërtet, Allahu e realizon çështjen e Tij.",source:"Talak: 3"},{text:"Dhe jepni lajmin e mirë të durueshmëve, të cilët kur i godet ndonjë fatkeqësi, thonë: 'Ne jemi të Allahut dhe tek Ai do të kthehemi'.",source:"Bekare: 155-156"},{text:"Dhe mos humbni shpresën nga mëshira e Allahut. Vërtet, nga mëshira e Allahut nuk humbin shpresën, përveç popullit jobesimtar.",source:"Jusuf: 87"}],by=["E Diel","E Hënë","E Martë","E Mërkurë","E Enjte","E Premte","E Shtunë"],py=["Janar","Shkurt","Mars","Prill","Maj","Qershor","Korrik","Gusht","Shtator","Tetor","Nëntor","Dhjetor"],Sy=f=>{const h=by[f.getDay()],m=f.getDate(),s=py[f.getMonth()],g=f.getFullYear();return`${m}, ${s} ${g} / ${h}`},Ve={imsaku:"Imsaku",sunrise:"L. e Diellit",dreka:"Dreka",ikindia:"Ikindia",akshami:"Akshami",jacia:"Jacia"};function xy(){const[f,h]=V.useState(!0),[m,s]=V.useState(new Date),[g,E]=V.useState(!1),[M,U]=V.useState({imam:"",mosqueName:"",location:"",showIqamah:!1}),A={imsaku:"02:47",sunrise:"04:59",dreka:"12:44",ikindia:"16:47",akshami:"20:22",jacia:"22:24"},[p,N]=V.useState(Ef[0]);V.useEffect(()=>{const L=setInterval(()=>{s(new Date)},1e3);return()=>clearInterval(L)},[]),V.useEffect(()=>{const L=setInterval(()=>{E(rt=>rt?(N(Ef[Math.floor(Math.random()*Ef.length)]),!1):!0)},15e3);return()=>clearInterval(L)},[]);const X=L=>{L.preventDefault(),h(!1)},Q=(L,rt)=>{U(et=>({...et,[L]:rt}))},ft=()=>{const L=m.getHours()*60+m.getMinutes(),rt=[{name:"imsaku",time:A.imsaku},{name:"sunrise",time:A.sunrise},{name:"dreka",time:A.dreka},{name:"ikindia",time:A.ikindia},{name:"akshami",time:A.akshami},{name:"jacia",time:A.jacia}];for(let et=0;et<rt.length;et++){const[_t,F]=rt[et].time.split(":").map(Number),Et=_t*60+F;if(L<Et)return rt[et].name}return"imsaku"},P=()=>{const L=m.getHours()*60+m.getMinutes(),rt=[{name:"imsaku",time:A.imsaku,label:Ve.imsaku},{name:"sunrise",time:A.sunrise,label:Ve.sunrise},{name:"dreka",time:A.dreka,label:Ve.dreka},{name:"ikindia",time:A.ikindia,label:Ve.ikindia},{name:"akshami",time:A.akshami,label:Ve.akshami},{name:"jacia",time:A.jacia,label:Ve.jacia}];for(let Mt=0;Mt<rt.length;Mt++){const[zl,Pt]=rt[Mt].time.split(":").map(Number),Ot=zl*60+Pt;if(L<Ot){const ul=Ot-L,It=Math.floor(ul/60),Tt=ul%60;return{prayer:rt[Mt].label,time:It>0?`${It} orë e ${Tt} minuta`:`${Tt} minuta`}}}const[et,_t]=rt[0].time.split(":").map(Number),F=et*60+_t,Et=24*60-L+F,Rt=Math.floor(Et/60),k=Et%60;return{prayer:rt[0].label,time:Rt>0?`${Rt} orë e ${k} minuta`:`${k} minuta`}},nt=L=>{switch(L){case"imsaku":case"akshami":case"jacia":return C.jsx(ry,{className:"w-6 h-6"});case"sunrise":return C.jsx(gy,{className:"w-6 h-6"});default:return C.jsx(vy,{className:"w-6 h-6"})}};if(f)return C.jsxs("div",{className:"min-h-screen bg-cover bg-center bg-no-repeat flex items-center justify-center p-4",style:{backgroundImage:`url(${Qd})`},children:[C.jsx("div",{className:"absolute inset-0 bg-black/40"}),C.jsxs(ly,{className:"w-full max-w-md relative z-10 shadow-2xl",children:[C.jsx(ey,{children:C.jsx(ay,{className:"text-center text-xl flex items-center justify-center gap-2",children:"📝 Plotëso Formularin"})}),C.jsx(ny,{children:C.jsxs("form",{onSubmit:X,className:"space-y-4",children:[C.jsxs("div",{className:"space-y-2",children:[C.jsx(Fu,{htmlFor:"imam",className:"text-green-600 font-semibold flex items-center gap-2",children:"🕌 Imami:"}),C.jsx(Af,{id:"imam",value:M.imam,onChange:L=>Q("imam",L.target.value),className:"border-green-500 focus:border-green-600 transition-colors",placeholder:"Emri i imamit..."})]}),C.jsxs("div",{className:"space-y-2",children:[C.jsx(Fu,{htmlFor:"mosqueName",className:"text-orange-600 font-semibold flex items-center gap-2",children:"🏛️ Emri i Xhamise:"}),C.jsx(Af,{id:"mosqueName",value:M.mosqueName,onChange:L=>Q("mosqueName",L.target.value),className:"border-orange-500 focus:border-orange-600 transition-colors",placeholder:"Emri i xhamisë..."})]}),C.jsxs("div",{className:"space-y-2",children:[C.jsx(Fu,{htmlFor:"location",className:"text-teal-600 font-semibold flex items-center gap-2",children:"📍 Lokacioni:"}),C.jsx(Af,{id:"location",value:M.location,onChange:L=>Q("location",L.target.value),className:"border-teal-500 focus:border-teal-600 transition-colors",placeholder:"Qyteti, shteti..."})]}),C.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[C.jsx(ty,{id:"showIqamah",checked:M.showIqamah,onCheckedChange:L=>Q("showIqamah",L)}),C.jsx(Fu,{htmlFor:"showIqamah",className:"font-medium",children:"Shfaq Ikametin"})]}),C.jsx(Cv,{type:"submit",className:"w-full bg-teal-600 hover:bg-teal-700 text-white font-semibold py-3 transition-colors",children:"✅ Ruaje"})]})})]})]});const ot=P();return C.jsxs("div",{className:"min-h-screen bg-cover bg-center bg-no-repeat text-white relative",style:{backgroundImage:`url(${Qd})`},children:[C.jsx("div",{className:"absolute inset-0 bg-black/50"}),C.jsx("button",{onClick:()=>h(!0),className:"absolute top-4 right-4 z-10 p-3 bg-green-600 rounded-lg hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl",children:C.jsx(my,{className:"w-6 h-6"})}),C.jsxs("div",{className:"relative z-10 p-8",children:[C.jsx("div",{className:"text-center mb-8",children:C.jsx("div",{className:"text-6xl font-bold mb-4 text-shadow-lg",children:m.toLocaleTimeString("en-GB")})}),C.jsx("div",{className:"max-w-4xl mx-auto mb-8 min-h-[200px] flex items-center justify-center",children:g?C.jsx("div",{className:"text-center bg-black/30 p-6 rounded-xl backdrop-blur-sm",children:C.jsxs("p",{className:"text-3xl font-bold mb-2",children:[ot.prayer," edhe ",ot.time]})}):C.jsxs("div",{className:"bg-black/30 p-6 rounded-xl backdrop-blur-sm",children:[C.jsx("p",{className:"text-sm mb-3 opacity-90",children:"Disa nga Selefi kanë thënë:"}),C.jsx("p",{className:"text-2xl leading-relaxed mb-4",children:p.text}),C.jsxs("p",{className:"text-lg opacity-80 text-right",children:["(",p.source,")"]})]})}),C.jsxs("div",{className:"flex justify-between items-center mb-8 max-w-6xl mx-auto",children:[C.jsx("div",{className:"text-lg font-medium",children:Sy(m)}),C.jsx("div",{className:"text-lg font-medium",children:"15/ Muharrem /1447 AH"})]}),C.jsx("div",{className:"grid grid-cols-6 gap-4 max-w-6xl mx-auto",children:Object.entries(A).map(([L,rt])=>{const et=ft()===L;return C.jsxs("div",{className:`text-center p-4 rounded-lg transition-all duration-300 ${et?"bg-yellow-600/90 scale-105 shadow-lg":"bg-black/40 hover:bg-black/50"}`,children:[C.jsx("div",{className:"flex justify-center mb-2",children:nt(L)}),C.jsx("div",{className:"text-lg font-semibold mb-1",children:Ve[L]}),C.jsx("div",{className:"text-xl font-bold",children:rt})]},L)})}),(M.mosqueName||M.location)&&C.jsx("div",{className:"absolute bottom-4 left-4 text-sm bg-black/40 p-3 rounded-lg backdrop-blur-sm",children:C.jsxs("div",{className:"flex items-center space-x-2",children:[C.jsx("span",{children:"🏛️"}),C.jsxs("span",{children:[M.mosqueName," - ",M.location]})]})}),M.imam&&C.jsx("div",{className:"absolute bottom-4 right-4 text-sm bg-black/40 p-3 rounded-lg backdrop-blur-sm",children:C.jsxs("div",{className:"flex items-center space-x-2",children:[C.jsx("span",{children:"👤"}),C.jsxs("span",{children:["Imam azam: ",M.imam]})]})})]})]})}V0.createRoot(document.getElementById("root")).render(C.jsx(V.StrictMode,{children:C.jsx(xy,{})}));
