{"version": 3, "file": "yup.module.js", "sources": ["../src/yup.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\nimport * as Yup from 'yup';\n\n/**\n * Why `path!` ? because it could be `undefined` in some case\n * https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n */\nfunction parseErrorSchema(\n  error: Yup.ValidationError,\n  validateAllFieldCriteria: boolean,\n) {\n  return (error.inner || []).reduce<Record<string, FieldError>>(\n    (previous, error) => {\n      if (!previous[error.path!]) {\n        previous[error.path!] = { message: error.message, type: error.type! };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = previous[error.path!].types;\n        const messages = types && types[error.type!];\n\n        previous[error.path!] = appendErrors(\n          error.path!,\n          validateAllFieldCriteria,\n          previous,\n          error.type!,\n          messages\n            ? ([] as string[]).concat(messages as string[], error.message)\n            : error.message,\n        ) as FieldError;\n      }\n\n      return previous;\n    },\n    {},\n  );\n}\n\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions?: Parameters<(typeof schema)['validate']>[1],\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Yup.InferType<typeof schema>>;\n\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions: Parameters<(typeof schema)['validate']>[1] | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Yup schema validation\n * @param {Yup.ObjectSchema<TFieldValues> | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<TFieldValues>>>} schema - Yup validation schema\n * @param {Parameters<(typeof schema)['validate']>[1]} schemaOptions - Options to pass to Yup's validate/validateSync\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {('async' | 'sync')} [resolverOptions.mode] - Validation mode\n * @param {boolean} [resolverOptions.raw] - If true, returns raw values instead of validated results\n * @returns {Resolver<Yup.InferType<typeof schema> | Input>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Yup.object({\n *   name: Yup.string().required(),\n *   age: Yup.number().required(),\n * });\n *\n * useForm({\n *   resolver: yupResolver(schema)\n * });\n */\nexport function yupResolver<Input extends FieldValues, Context, Output>(\n  schema:\n    | Yup.ObjectSchema<Input, any, Output, any>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<Input, any, Output, any>>>,\n  schemaOptions?: Parameters<(typeof schema)['validate']>[1],\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Yup.InferType<typeof schema> | Input> {\n  return async (values: Input, context, options) => {\n    try {\n      if (schemaOptions?.context && process.env.NODE_ENV === 'development') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          \"You should not used the yup options context. Please, use the 'useForm' context object instead\",\n        );\n      }\n\n      const result = await schema[\n        resolverOptions.mode === 'sync' ? 'validateSync' : 'validate'\n      ](\n        values,\n        Object.assign({ abortEarly: false }, schemaOptions, { context }),\n      );\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        values: resolverOptions.raw ? Object.assign({}, values) : result,\n        errors: {},\n      };\n    } catch (e: any) {\n      if (!e.inner) {\n        throw e;\n      }\n\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            e,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n  };\n}\n"], "names": ["yupResolver", "schema", "schemaOptions", "resolverOptions", "values", "context", "options", "Promise", "resolve", "process", "env", "NODE_ENV", "console", "warn", "mode", "Object", "assign", "abort<PERSON><PERSON><PERSON>", "then", "result", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "errors", "_catch", "e", "inner", "toNestErrors", "error", "validateAllFieldCriteria", "criteriaMode", "reduce", "previous", "path", "message", "type", "types", "messages", "appendErrors", "concat", "reject"], "mappings": "8HAoFM,SAAUA,EACdC,EAGAC,EACAC,GAKA,gBALAA,IAAAA,EAGI,CAAA,GAEJ,SAAcC,EAAeC,EAASC,GAAO,WAAIC,QAAAC,iCAEzCN,MAAAA,GAAAA,EAAeG,SAAoC,gBAAzBI,QAAQC,IAAIC,UAExCC,QAAQC,KACN,iGAEHN,QAAAC,QAEoBP,EACM,SAAzBE,EAAgBW,KAAkB,eAAiB,YAEnDV,EACAW,OAAOC,OAAO,CAAEC,YAAY,GAASf,EAAe,CAAEG,QAAAA,MACvDa,cALKC,GASN,OAFAb,EAAQc,2BAA6BC,EAAuB,CAAA,EAAIf,GAEzD,CACLF,OAAQD,EAAgBmB,IAAMP,OAAOC,OAAO,CAAE,EAAEZ,GAAUe,EAC1DI,OAAQ,GACR,6DArB2CC,GAsB9C,SAAQC,GACP,IAAKA,EAAEC,MACL,MAAMD,EAGR,MAAO,CACLrB,OAAQ,GACRmB,OAAQI,GA7GdC,EA+GUH,EA9GVI,GA+GWvB,EAAQc,2BACkB,QAAzBd,EAAQwB,cA9GZF,EAAMF,OAAS,IAAIK,OACzB,SAACC,EAAUJ,GAKT,GAJKI,EAASJ,EAAMK,QAClBD,EAASJ,EAAMK,MAAS,CAAEC,QAASN,EAAMM,QAASC,KAAMP,EAAMO,OAG5DN,EAA0B,CAC5B,IAAMO,EAAQJ,EAASJ,EAAMK,MAAOG,MAC9BC,EAAWD,GAASA,EAAMR,EAAMO,MAEtCH,EAASJ,EAAMK,MAASK,EACtBV,EAAMK,KACNJ,EACAG,EACAJ,EAAMO,KACNE,EACK,GAAgBE,OAAOF,EAAsBT,EAAMM,SACpDN,EAAMM,QAEd,CAEA,OAAOF,CACT,EACA,CAAA,IAyFM1B,IApHV,IACEsB,EACAC,CAqHE,GACF,CAAC,MAAAJ,GAAA,OAAAlB,QAAAiC,OAAAf,EACH,CAAA,CAAA"}